# France API

A RESTful API built using **Fastify** and **Prisma** for database access.

---

## 🚀 Features

- ⚡ Ultra-fast Fastify web server
- 📦 Prisma ORM with PostgreSQL
- 📄 RESTful API structure
- 🔄 Auto migrations
- 🧪 Ready for unit & integration testing

---

## 🛠️ Tech Stack

- [Fastify](https://www.fastify.io/) – Fast and low-overhead web framework
- [Prisma ORM](https://www.prisma.io/) – Modern ORM with type safety
- [PostgreSQL](https://www.postgresql.org/) – (or any Prisma-supported database)
- [Typebox](https://github.com/sinclairzx81/typebox) – Input validation (optional)

---

## 📦 Install Dependencies

```bash
npm install
```

---

## 🧪 Setup Environment

Create a `.env` from `.env.example` file and fill in the required variables:

```bash
cp .env.example .env
```

---

## 🔄 Prisma Setup

Apply migrations and seed database:

```bash
npm run prisma:deploy
npm run prisma:seed
```

View your database in browser (optional):

```bash
npm run prisma:studio
```

---

## 🚀 Run development Server

```bash
npm run dev
```

App runs on: [http://localhost:3000](http://localhost:3000)  
Swagger UI: [http://localhost:3000/api/swagger](http://localhost:3000/api/swagger)  
RapiDoc: [http://localhost:3000/api/rapidoc](http://localhost:3000/api/rapidoc)

---

## Build & Run

```bash
npm run build
npm run start
```

---

## 🔒 License

MIT
