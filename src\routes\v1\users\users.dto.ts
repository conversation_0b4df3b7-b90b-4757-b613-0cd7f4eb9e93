import {
  PaginationSchema,
  PasswordSchema,
  PhoneSchema,
  SearchSchema,
} from '@/schema/global.schema';
import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Type } from '@sinclair/typebox';
import { ModelSchemas } from '@/schema/model.schema';

const UpdatePasswordSchema = Type.Object({
  oldPassword: PasswordSchema,
  newPassword: PasswordSchema,
});

const UpdateProfileSchema = Type.Object({
  avatar: Type.Optional(Type.Any({ isFile: true })),
  address: Type.Optional(Type.String({ minLength: 1 })),
  dob: Type.Optional(Type.String({ format: 'date' })),
  gender: Type.Optional(ModelSchemas.GenderSchema),
  //
  givenName: Type.Optional(Type.String({ minLength: 1 })),
  familyName: Type.Optional(Type.String({ minLength: 1 })),
  phone: Type.Optional(PhoneSchema),
  city: Type.Optional(Type.String({ minLength: 1 })),
  country: Type.Optional(Type.String({ minLength: 1 })),
  expertiseDomains: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
  frenchLevel: Type.Optional(Type.String({ minLength: 1 })),
  otherLanguages: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
  experience: Type.Optional(Type.String({ minLength: 1 })),
  education: Type.Optional(Type.String({ minLength: 1 })),
  livedFrance: Type.Optional(Type.String({ minLength: 1 })),
  jobAvailability: Type.Optional(Type.String({ minLength: 1 })),
  geoMobility: Type.Optional(Type.String({ minLength: 1 })),
});

const UpdateResumeSchema = Type.Object({
  resume: Type.Any({ isFile: true }),
});

// Admin

const GetUsersSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
  role: Type.Optional(ModelSchemas.RoleSchema),
});

const GetUserByIdSchema = Type.Object({
  id: Type.Number(),
});

const CreateUserSchema = Type.Object({
  email: Type.String({ format: 'email', minLength: 1 }),
  password: PasswordSchema,
  name: Type.String({ minLength: 1 }),
  role: Type.Optional(ModelSchemas.RoleSchema),
});

const UpdateUserActiveParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateUserActiveBodySchema = Type.Object({
  active: Type.Boolean(),
});

const DeleteUserByIdSchema = Type.Object({
  id: Type.Number(),
});

export const UsersDTO = {
  UpdatePasswordSchema,
  UpdateProfileSchema,
  UpdateResumeSchema,
  // Admin
  GetUsersSchema,
  GetUserByIdSchema,

  UpdateUserActiveParamsSchema,
  UpdateUserActiveBodySchema,

  CreateUserSchema,
  DeleteUserByIdSchema,
};

export type UserDTOTypes = StaticFromSchema<typeof UsersDTO>;
