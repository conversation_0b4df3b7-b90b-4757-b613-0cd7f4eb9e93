import jobsService from './jobs.service';

import { FastifyRequest, FastifyReply } from 'fastify';
import { JobDTOTypes } from './jobs.dto';
import { serializeJob } from '@/common/utils/serialize';
import { Role } from '@prisma/client';

class JobsController {
  async createJob(request: FastifyRequest, reply: FastifyReply) {
    const { companyId, ...jobData } = request.body as JobDTOTypes['CreateJob'];

    let _addCompanyId = companyId;

    if (request.person.role == Role.EMPLOYER) {
      const _companyId = request.person.employer?.companyId;
      if (!_companyId) throw request.server.errors.UNAUTHORIZED;
      if (companyId && companyId != _companyId) throw request.server.errors.FORBIDDEN;
      _addCompanyId = _companyId;
    }

    if (!_addCompanyId) throw request.server.errors.BAD_REQUEST;

    const job = await jobsService.create({ companyId: _addCompanyId, ...jobData });
    reply.sendJson(serializeJob(job));
  }

  async getJobById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as JobDTOTypes['GetJob'];
    const job = await jobsService.getById(id);
    if (!job) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializeJob(job));
  }

  async getJobByIdForAdmin(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as JobDTOTypes['GetJob'];

    let employerCompanyId: number | undefined;
    if (request.person.role == Role.EMPLOYER) {
      const _companyId = request.person.employer?.companyId;
      if (!_companyId) throw request.server.errors.UNAUTHORIZED;
      employerCompanyId = _companyId;
    }

    const job = await jobsService.getById(id, {
      include: { tags: true, company: true, applications: true },
    });

    if (!job) throw request.server.errors.NOT_FOUND;
    if (employerCompanyId && job.companyId != employerCompanyId)
      throw request.server.errors.FORBIDDEN;

    reply.sendJson(serializeJob(job));
  }

  async getJobs(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search, companyId, tag } = request.query as JobDTOTypes['GetJobs'];
    const { jobs, pagination } = await jobsService.getList({ limit, page, search, companyId, tag });

    reply.sendJson(
      jobs.map((job) => serializeJob(job)),
      pagination,
    );
  }

  async getJobsForAdmin(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, companyId, search, tag } = request.query as JobDTOTypes['GetJobs'];

    let comparedCompanyId = companyId;

    if (request.person.role == Role.EMPLOYER) {
      const _companyId = request.person.employer?.companyId;
      if (!_companyId) throw request.server.errors.UNAUTHORIZED;
      if (companyId && companyId != _companyId) throw request.server.errors.FORBIDDEN;
      comparedCompanyId = _companyId;
    }

    const { jobs, pagination } = await jobsService.getList(
      { limit, page, search, tag, companyId: comparedCompanyId },
      { include: { tags: true, company: true, applications: true } },
    );

    reply.sendJson(
      jobs.map((job) => serializeJob(job)),
      pagination,
    );
  }

  async updateJob(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as JobDTOTypes['UpdateJobParams'];
    const { ...data } = request.body as JobDTOTypes['UpdateJobBody'];

    const oldJob = await jobsService.getById(id);
    if (!oldJob) throw request.server.errors.NOT_FOUND;

    const job = await jobsService.updateById(id, { ...data });
    reply.sendJson(serializeJob(job));
  }

  async deleteJob(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as JobDTOTypes['DeleteJob'];
    const job = await jobsService.getById(id);
    if (!job) throw request.server.errors.NOT_FOUND;
    await jobsService.deleteById(id);
    reply.sendJson();
  }

  async applyJob(request: FastifyRequest, reply: FastifyReply) {
    const { jobId } = request.body as JobDTOTypes['ApplyJob'];
    const job = await jobsService.getById(jobId);
    if (!job) throw request.server.errors.NOT_FOUND;
    await jobsService.applyJob(request.person.id, jobId);
    reply.sendJson();
  }
}

export default new JobsController();
