import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma, TagType } from '@prisma/client';
import { normalizeTag, normalizeTags } from '@/common/utils/serialize';

type JobServiceOptions = {
  include?: Prisma.JobInclude;
  omit?: Prisma.JobOmit;
};

const defaultInclude: Prisma.JobInclude = { tags: true, company: true };

class JobsService {
  async create(
    data: {
      title: string;
      description: string;
      requirements: string[];
      benefits: string[];
      salary: string;
      companyId: number;
      tags?: string[];
    },
    options: JobServiceOptions = {},
  ) {
    const { tags = [], ...jobData } = data;
    const { include = defaultInclude, omit } = options;

    const normalizedTags = normalizeTags(tags);

    return await withTransaction(async (tx) => {
      return await tx.job.create({
        include,
        omit,
        data: {
          ...jobData,
          tags: {
            connectOrCreate: normalizedTags.map((name) => ({
              where: { name_type: { name, type: TagType.JOB } },
              create: { name: name, type: TagType.JOB },
            })),
          },
        },
      });
    });
  }

  async updateById(
    id: number,
    data: {
      title?: string;
      description?: string;
      requirements?: string[];
      benefits?: string[];
      salary?: string;
      tags?: string[];
    },
    options: JobServiceOptions = {},
  ) {
    const { tags = [], ...jobData } = data;
    const { include = defaultInclude, omit } = options;

    const normalizedTags = normalizeTags(tags);

    return await withTransaction(async (tx) => {
      return await tx.job.update({
        where: { id },
        include,
        omit,
        data: {
          ...jobData,
          tags: {
            set: [],
            connectOrCreate: normalizedTags.map((name) => ({
              where: { name_type: { name, type: TagType.JOB } },
              create: { name, type: TagType.JOB },
            })),
          },
        },
      });
    });
  }

  async getById(id: number, options: JobServiceOptions = {}) {
    const { include = defaultInclude, omit } = options;
    return await prisma.job.findUnique({ where: { id }, include, omit });
  }

  async getList(
    query: {
      limit?: number;
      page?: number;
      search?: string;
      companyId?: number;
      tag?: string;
    },
    options: JobServiceOptions = {},
  ) {
    const { limit = 10, page = 1, search, companyId, tag } = query;
    const { include = defaultInclude, omit } = options;

    const where: Prisma.JobWhereInput = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { tags: { some: { name: { contains: search, mode: 'insensitive' } } } },
      ];
    }

    if (tag) {
      const normalizedTag = normalizeTag(tag);
      if (where.OR || where.companyId) {
        where.AND = [{ tags: { some: { name: { equals: normalizedTag } } } }];
      } else {
        where.tags = { some: { name: { equals: normalizedTag } } };
      }
    }

    if (companyId) {
      where.companyId = companyId;
    }

    const [jobs, count] = await prisma.$transaction([
      prisma.job.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
        include,
        omit,
      }),
      prisma.job.count({ where }),
    ]);

    return { jobs, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.job.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  async applyJob(personId: number, jobId: number) {
    return await prisma.application.create({
      data: { personId, jobId },
    });
  }
}

export default new JobsService();
