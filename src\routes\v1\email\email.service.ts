import { prisma } from '@/plugins/prisma.plugin';
import { Prisma } from '@prisma/client';

class EmailService {
  async subscribe(email: string) {
    const existing = await prisma.emailSubscribe.findUnique({ where: { email } });

    if (!existing) {
      return await prisma.emailSubscribe.create({ data: { email } });
    }

    if (existing.deletedAt) {
      return await prisma.emailSubscribe.update({
        where: { email },
        data: { deletedAt: null },
      });
    }

    return existing;
  }

  async unsubscribe(email: string) {
    return await prisma.emailSubscribe.update({
      where: { email },
      data: { deletedAt: new Date() },
    });
  }

  async getList(options: { limit?: number; page?: number; search?: string }) {
    const { limit = 10, page = 1, search } = options;
    const where: Prisma.EmailSubscribeWhereInput = {};

    if (search) {
      where.email = { contains: search, mode: 'insensitive' };
    }

    const [emailSubscribes, count] = await prisma.$transaction([
      prisma.emailSubscribe.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
      }),
      prisma.emailSubscribe.count({ where }),
      prisma.emailSubscribe.count(),
    ]);

    return {
      emailSubscribes,
      pagination: { total: count, page, limit },
      total: emailSubscribes.length,
    };
  }
}

export default new EmailService();
