import { Options as AjvOptions } from '@fastify/ajv-compiler';
import type { Ajv } from 'ajv';

export function ajvFilePlugin(ajv: Ajv) {
  return ajv.addKeyword({
    keyword: 'isFile',
    error: { message: 'should be a file' },
    compile: (_schema, parent) => {
      parent.type = 'string';
      parent.format = 'binary';
      delete parent.isFile;
      return (data: string) => data.length > 1;
    },
  });
}

const ajvPlugin: { customOptions?: AjvOptions; plugins?: any[] } = {
  customOptions: {
    coerceTypes: 'array',
    removeAdditional: 'all',
    useDefaults: true,
    allErrors: false,
  },
  plugins: [ajvFilePlugin],
};

export default ajvPlugin;
