import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Nullable } from './global.schema';
import { Type } from '@sinclair/typebox';
import { Gender, Role } from '@prisma/client';

const RoleSchema = Type.String({ enum: [...Object.values(Role)] });
const GenderSchema = Type.String({ enum: [...Object.values(Gender)] });

const CompanySchema = Type.Object({
  id: Type.Number(),
  name: Type.String(),
  address: Type.String(),
  description: Nullable(Type.String()),
  website: Nullable(Type.String()),
  logoUrl: Nullable(Type.String()),
  videoUrl: Nullable(Type.String()),
  services: Type.Array(Type.String()),
  highlights: Type.Array(Type.String()),
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

const JobSchema = Type.Object({
  id: Type.Number(),
  title: Type.String(),
  description: Type.String(),
  requirements: Nullable(Type.String()),
  salary: Nullable(Type.String()),
  companyId: Type.Number(),
  company: CompanySchema,
  tags: Type.Array(Type.String()),
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

const QuizSchema = Type.Object({
  id: Type.Number(),
  title: Type.String(),
  week: Type.Number(),
  description: Nullable(Type.String()),
  kahootUrl: Type.String(),
  imageUrl: Nullable(Type.String()),
  tags: Type.Array(Type.String()),
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

const SchoolTypeSchema = Type.Object({
  id: Type.Number(),
  name: Type.String(),
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

const SchoolSchema = Type.Object({
  id: Type.Number(),
  name: Type.String(),
  city: Nullable(Type.String()),
  address: Nullable(Type.String()),
  description: Nullable(Type.String()),
  logoUrl: Nullable(Type.String()),
  website: Nullable(Type.String()),
  latitude: Nullable(Type.Number()),
  longitude: Nullable(Type.Number()),
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

const ProfileSchema = Type.Object({
  avatar: Nullable(Type.String()),
  address: Nullable(Type.String()),
  dob: Nullable(Type.String({ format: 'date' })),
  gender: GenderSchema,
  resumeUrl: Nullable(Type.String()),
  completedAt: Nullable(Type.String({ format: 'date-time' })),
  //
  givenName: Nullable(Type.String()),
  familyName: Nullable(Type.String()),
  phone: Nullable(Type.String()),
  city: Nullable(Type.String()),
  country: Nullable(Type.String()),
  expertiseDomains: Type.Array(Type.String()),
  frenchLevel: Nullable(Type.String()),
  otherLanguages: Type.Array(Type.String()),
  experience: Nullable(Type.String()),
  education: Nullable(Type.String()),
  livedFrance: Nullable(Type.String()),
  jobAvailability: Nullable(Type.String()),
  geoMobility: Nullable(Type.String()),
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

const EmployerSchema = Type.Object({
  companyId: Type.Number(),
  company: CompanySchema,
});

const UserSchema = Type.Object({
  id: Type.Number(),
  email: Type.String(),
  name: Type.String(),
  role: RoleSchema,
  active: Type.Boolean(),
  refSource: Nullable(Type.String()),
  profile: ProfileSchema,
  employer: Nullable(EmployerSchema),
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

const CategorySchema = Type.Object({
  id: Type.Number(),
  name: Type.String(),
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

const ArticleSchema = Type.Object({
  id: Type.Number(),
  title: Type.String(),
  summary: Type.String(),
  slug: Type.String(),
  thumbnailUrl: Nullable(Type.String()),
  content: Nullable(Type.String()),
  redirectUrl: Nullable(Type.String()),
  category: CategorySchema,
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

const ChallengeSchema = Type.Object({
  id: Type.Number(),
  title: Type.String(),
  description: Type.String(),
  thumbnailUrl: Nullable(Type.String()),
  articleUrl: Nullable(Type.String()),
  // createdAt: Type.String({ format: 'date-time' }),
  // updatedAt: Type.String({ format: 'date-time' }),
});

export const ModelSchemas = {
  RoleSchema,
  GenderSchema,
  UserSchema,
  ProfileSchema,
  EmployerSchema,
  QuizSchema,
  SchoolTypeSchema,
  SchoolSchema,
  CompanySchema,
  JobSchema,
  ArticleSchema,
  ChallengeSchema,
  CategorySchema,
};

export type ModelSchemaTypes = StaticFromSchema<typeof ModelSchemas>;
