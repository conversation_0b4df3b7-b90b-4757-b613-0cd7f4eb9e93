(function () {
  const originalSwaggerUIBundle = window.SwaggerUIBundle;

  function wrappedSwaggerUIBundle(...args) {
    const ui = originalSwaggerUIBundle(...args);
    window.ui = ui;
    return ui;
  }

  Object.assign(wrappedSwaggerUIBundle, originalSwaggerUIBundle);
  window.SwaggerUIBundle = wrappedSwaggerUIBundle;

  const originalFetch = window.fetch;
  const watchedEndpoints = ['/login', '/register'];

  window.fetch = async function () {
    const url = arguments[0];
    const matched = watchedEndpoints.some((endpoint) => url.includes(endpoint));
    const response = await originalFetch.apply(this, arguments);

    if (matched && response.ok) {
      try {
        const clone = response.clone();
        const json = await clone.json();
        const token = json.data?.accessToken;

        if (token) {
          console.log('Access Token received:', token);
          window.ui.preauthorizeApiKey('bearerAuth', token);
        }
      } catch (err) {
        console.error('Error processing token from response:', err);
      }
    }

    return response;
  };
})();
