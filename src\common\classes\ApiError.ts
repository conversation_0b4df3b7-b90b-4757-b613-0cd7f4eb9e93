export class ApiError extends Error {
  statusCode: number;
  customCode: number;
  code: string;
  errors?: unknown;

  constructor({
    message,
    statusCode = 500,
    customCode,
    code,
    errors,
  }: {
    message: string;
    statusCode?: number;
    customCode?: number;
    code: string;
    errors?: unknown;
  }) {
    super(message);
    this.statusCode = statusCode;
    this.customCode = customCode || statusCode;
    this.code = code;
    this.errors = errors;
  }
}
