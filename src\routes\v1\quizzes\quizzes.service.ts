import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma, TagType } from '@prisma/client';
import { normalizeTag, normalizeTags } from '@/common/utils/serialize';

class QuizzesService {
  async create(data: {
    title: string;
    description: string;
    week: number;
    kahootUrl: string;
    tags?: string[];
    imageKey?: string;
  }) {
    const { tags = [], imageKey, ...quizData } = data;
    const normalizedTags = normalizeTags(tags);

    return await withTransaction(async (tx) => {
      return await tx.quiz.create({
        include: { tags: true },
        data: {
          ...quizData,
          imageKey,
          tags: {
            connectOrCreate: normalizedTags.map((name) => ({
              where: { name_type: { name, type: TagType.QUIZ } },
              create: { name, type: TagType.QUIZ },
            })),
          },
        },
      });
    });
  }

  async updateById(
    id: number,
    data: {
      title?: string;
      description?: string;
      kahootUrl?: string;
      week?: number;
      tags?: string[];
      imageKey?: string;
    },
  ) {
    const { tags = [], imageKey, ...quizData } = data;
    const normalizedTags = normalizeTags(tags);

    return await withTransaction(async (tx) => {
      return await tx.quiz.update({
        where: { id },
        include: { tags: true },
        data: {
          ...quizData,
          imageKey,
          tags:
            tags.length > 0
              ? {
                  set: [],
                  connectOrCreate: normalizedTags.map((name) => ({
                    where: { name_type: { name, type: TagType.QUIZ } },
                    create: { name, type: TagType.QUIZ },
                  })),
                }
              : undefined,
        },
      });
    });
  }

  async getById(id: number) {
    return await prisma.quiz.findUnique({ where: { id }, include: { tags: true } });
  }

  async getList(options: { limit?: number; page?: number; search?: string; tag?: string }) {
    const { limit = 10, page = 1, search, tag } = options;

    const where: Prisma.QuizWhereInput = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { tags: { some: { name: { contains: search, mode: 'insensitive' } } } },
      ];
    }

    if (tag) {
      const normalizedTag = normalizeTag(tag);

      if (where.OR) {
        where.AND = [{ tags: { some: { name: { equals: normalizedTag } } } }];
      } else {
        where.tags = { some: { name: { equals: normalizedTag } } };
      }
    }

    const [quizzes, count] = await prisma.$transaction([
      prisma.quiz.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
        include: { tags: true },
      }),
      prisma.quiz.count({ where }),
    ]);

    return { quizzes, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.quiz.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
}

export default new QuizzesService();
