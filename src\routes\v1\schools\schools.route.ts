import schoolsController from './schools.controller';

import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { SchoolDTO } from './schools.dto';
import { ModelSchemas } from '@/schema/model.schema';

export default async function schoolsRoutes(app: FastifyInstance) {
  app.get('/schools', {
    handler: schoolsController.getSchools,
    schema: {
      summary: 'Get schools',
      description: 'Get schools',
      tags: ['school'],
      security: [],
      querystring: SchoolDTO.GetSchoolsSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.SchoolSchema), true),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/schools/types', {
    handler: schoolsController.getSchoolTypes,
    schema: {
      summary: 'Get school types',
      description: 'Get school types',
      tags: ['school', 'school-type'],
      security: [],
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.SchoolTypeSchema)),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/schools/:id', {
    handler: schoolsController.getSchoolById,
    schema: {
      summary: 'Get school by id',
      description: 'Get school by id',
      tags: ['school'],
      security: [],
      params: SchoolDTO.GetSchoolSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.SchoolSchema),
        400: createErrorSchema(),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  // Admin

  app.get('/admin/schools/types', {
    handler: schoolsController.getSchoolTypes,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get school types',
      description: 'Get school types',
      tags: ['school', 'school-type'],
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.SchoolTypeSchema)),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/schools', {
    handler: schoolsController.getSchools,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get schools',
      description: 'Get schools',
      tags: ['school', 'admin'],
      querystring: SchoolDTO.GetSchoolsSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.SchoolSchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/schools/:id', {
    handler: schoolsController.getSchoolById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get school by id',
      description: 'Get school by id',
      tags: ['school', 'admin'],
      params: SchoolDTO.GetSchoolSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.SchoolSchema),
        400: createErrorSchema(),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/schools', {
    handler: schoolsController.createSchool,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create school',
      description: 'Create school',
      consumes: ['multipart/form-data'],
      tags: ['school', 'admin'],
      body: SchoolDTO.CreateSchoolSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.SchoolSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.patch('/admin/schools/:id', {
    handler: schoolsController.updateSchool,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update school information',
      description: 'Update school information',
      consumes: ['multipart/form-data'],
      tags: ['school', 'admin'],
      params: SchoolDTO.UpdateSchoolParamsSchema,
      body: SchoolDTO.UpdateSchoolBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.SchoolSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/schools/:id', {
    handler: schoolsController.deleteSchool,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete school',
      description: 'Delete school',
      tags: ['school', 'admin'],
      params: SchoolDTO.DeleteSchoolSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
