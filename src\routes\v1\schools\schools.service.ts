import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma } from '@prisma/client';

class SchoolsService {
  async create(data: {
    name: string;
    schoolTypeId: number;
    city?: string;
    address?: string;
    description?: string;
    website?: string;
    latitude?: number;
    longitude?: number;
    logoKey?: string;
  }) {
    return await withTransaction(async (tx) => {
      return await tx.school.create({ data });
    });
  }

  async updateById(
    id: number,
    data: {
      name?: string;
      schoolTypeId?: number;
      city?: string;
      address?: string;
      description?: string;
      website?: string;
      latitude?: number;
      longitude?: number;
      logoKey?: string;
    },
  ) {
    return await withTransaction(async (tx) => {
      return await tx.school.update({
        where: { id },
        data,
      });
    });
  }

  async getSchoolTypes() {
    return await prisma.schoolType.findMany();
  }

  async getById(id: number) {
    return await prisma.school.findUnique({ where: { id } });
  }

  async getList(options: {
    limit?: number;
    page?: number;
    search?: string;
    city?: string;
    schoolTypeId?: number;
  }) {
    const { limit = 10, page = 1, search, city, schoolTypeId } = options;

    const where: Prisma.SchoolWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { city: { contains: search, mode: 'insensitive' } },
        { address: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (city) {
      if (where.OR) {
        where.AND = [{ city: { contains: city, mode: 'insensitive' } }];
      } else {
        where.city = { contains: city, mode: 'insensitive' };
      }
    }

    if (schoolTypeId) {
      where.schoolTypeId = schoolTypeId;
    }

    const [schools, count] = await prisma.$transaction([
      prisma.school.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
      }),
      prisma.school.count({ where }),
    ]);

    return { schools, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.school.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
}

export default new SchoolsService();
