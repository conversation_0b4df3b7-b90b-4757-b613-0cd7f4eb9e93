import notificationsController from './notifications.controller';

import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { NotificationsDTO } from './notifications.dto';
import { Role } from '@prisma/client';

export default async function notificationsRoutes(app: FastifyInstance) {
  app.post('/notifications/subscribe', {
    handler: notificationsController.subscribe,
    schema: {
      summary: 'Subscribe to notifications',
      description: 'Subscribe to notifications',
      tags: ['notification'],
      security: [],
      body: NotificationsDTO.SubscribeSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  // Admin

  app.post('/admin/notifications/send', {
    handler: notificationsController.sendMessage,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Send notification',
      description: 'Send notification',
      tags: ['notification', 'admin'],
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });
}
