import { ApiError } from '@/common/classes/ApiError';
import { HttpErrors } from '@/common/constants/HttpErrors';
import fp from 'fastify-plugin';

export function findErrorByCode(customCode: number): ApiError {
  const error = Object.values(HttpErrors).find((error) => {
    return error instanceof ApiError && error.customCode === customCode;
  });

  return error ?? HttpErrors.INTERNAL_SERVER_ERROR;
}

const errorsPlugin = fp(async (app) => {
  app.decorate('errors', {
    // Base error class
    ApiError,

    // HTTP errors
    ...HttpErrors,
  });
});

export default errorsPlugin;
