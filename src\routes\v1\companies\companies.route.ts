import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { CompaniesDTO } from './companies.dto';
import { ModelSchemas } from '@/schema/model.schema';
import companiesController from './companies.controller';

export default async function companiesRoutes(app: FastifyInstance) {
  app.get('/companies', {
    handler: companiesController.getCompanies,
    schema: {
      summary: 'Get companies',
      description: 'Get companies',
      tags: ['company'],
      security: [],
      querystring: CompaniesDTO.GetCompaniesSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.CompanySchema), true),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/companies/:id', {
    handler: companiesController.getCompanyById,
    schema: {
      summary: 'Get company by id',
      description: 'Get company by id',
      tags: ['company'],
      security: [],
      params: CompaniesDTO.GetCompanySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.CompanySchema),
        400: createErrorSchema(),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/companies', {
    handler: companiesController.getCompanies,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get companies',
      description: 'Get companies',
      tags: ['company', 'admin'],
      querystring: CompaniesDTO.GetCompaniesSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.CompanySchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/companies/:id', {
    handler: companiesController.getCompanyById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get company by id',
      description: 'Get company by id',
      tags: ['company', 'admin'],
      params: CompaniesDTO.GetCompanySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.CompanySchema),
        400: createErrorSchema(),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/companies', {
    handler: companiesController.createCompany,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create company',
      description: 'Create company with employer',
      consumes: ['multipart/form-data'],
      tags: ['company', 'admin'],
      body: CompaniesDTO.CreateCompanySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.CompanySchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.patch('/admin/companies/:id', {
    handler: companiesController.updateCompany,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update company information',
      description: 'Update company information',
      consumes: ['multipart/form-data'],
      tags: ['company', 'admin'],
      params: CompaniesDTO.UpdateCompanyParamsSchema,
      body: CompaniesDTO.UpdateCompanyBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.CompanySchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/companies/:id', {
    handler: companiesController.deleteCompany,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete company',
      description: 'Delete company',
      tags: ['company', 'admin'],
      params: CompaniesDTO.DeleteCompanySchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
