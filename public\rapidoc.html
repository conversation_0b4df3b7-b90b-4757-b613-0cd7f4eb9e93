<!DOCTYPE html>
<html>
  <head>
    <title>RapiDoc - API Docs</title>
    <meta charset="utf-8" />
    <script src="https://unpkg.com/rapidoc/dist/rapidoc-min.js"></script>
  </head>
  <body style="margin: 0">
    <rapi-doc
      id="rapidoc"
      spec-url="/api/swagger/json"
      theme="dark"
      show-header="true"
      render-style="read"
      show-method-in-nav-bar="as-colored-block"
      primary-color="#4f46e5"
      font-size="medium"
      use-path-in-nav-bar="true"
      allow-try="true"
      show-components="true"
    />

    <script>
      const originalFetch = window.fetch;
      window.fetch = async (...args) => {
        const response = await originalFetch(...args);
        const url = args[0].url;

        if (typeof url === 'string' && url.includes('/login') && response.ok) {
          try {
            const clone = response.clone();
            const data = await clone.json();
            const token = data.data?.accessToken;
            if (token) {
              const rapidoc = document.getElementById('rapidoc');
              if (rapidoc) {
                rapidoc.setAttribute('api-key-name', 'Authorization');
                rapidoc.setAttribute('api-key-location', 'header');
                rapidoc.setAttribute('api-key-value', `Bearer ${token}`);
                console.log('Token set in RapiDoc', `Bearer ${token}`);
              }
            }
          } catch (err) {
            console.error('Error parsing response JSON:', err);
          }
        }

        return response;
      };
    </script>
  </body>
</html>
