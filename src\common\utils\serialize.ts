import { Challenge, Prisma, School } from '@prisma/client';
import { toFileUrl, toResumeUrl } from './helpers';

export function normalizeTag(tag: string) {
  return tag.trim().toLowerCase();
}

export function normalizeTags(tags: string[]) {
  return tags.map((tag) => normalizeTag(tag));
}

export function serializeQuiz(quiz: Prisma.QuizGetPayload<{ include: { tags: true } }>) {
  return {
    ...quiz,
    tags: quiz.tags.map((tag) => tag.name),
    imageUrl: toFileUrl(quiz.imageKey),
  };
}

export function serializeSchool(school: School) {
  return { ...school, logoUrl: toFileUrl(school.logoKey) };
}

export function serializeJob(
  job: Prisma.JobGetPayload<{ include: { tags: true; company: true } }>,
) {
  return {
    ...job,
    tags: job.tags.map((tag) => tag.name),
    company: { ...job.company, logoUrl: toFileUrl(job.company.logoKey) },
  };
}

export function serializeCompany(company: Prisma.CompanyGetPayload<{ include: { jobs: true } }>) {
  return {
    ...company,
    logoUrl: toFileUrl(company.logoKey),
  };
}

export function serializeArticle(article: Prisma.ArticleGetPayload<{ include: { tags: true } }>) {
  return {
    ...article,
    thumbnailUrl: toFileUrl(article.thumbnailKey),
    tags: article.tags.map((tag) => tag.name),
  };
}

export function serializeChallenge(challenge: Challenge) {
  return {
    ...challenge,
    thumbnailUrl: toFileUrl(challenge.thumbnailKey),
  };
}

export function serializePerson(person: Prisma.PersonGetPayload<{ include: { profile: true } }>) {
  if (!person.profile) return { ...person, password: undefined };

  return {
    ...person,
    password: undefined,
    profile: {
      ...person.profile,
      avatar: toFileUrl(person.profile.avatarKey),
      resumeUrl: toResumeUrl(person.profile.resumeKey),
    },
  };
}
