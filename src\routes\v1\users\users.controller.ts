import usersService from './users.service';
import { FastifyReply, FastifyRequest } from 'fastify';
import { UserDTOTypes } from './users.dto';
import { Gender, Role } from '@prisma/client';
import { serializePerson } from '@/common/utils/serialize';
import { fileTypeFromBuffer } from 'file-type';
import { RESUME_MIME_TYPES } from '@/common/constants/mime';
import { MinioBucket } from '@/common/constants/minio';

class UsersController {
  async getMe(request: FastifyRequest, reply: FastifyReply) {
    reply.sendJson(serializePerson(request.person));
  }

  async updatePassword(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.person;
    const { oldPassword, newPassword } = request.body as UserDTOTypes['UpdatePassword'];

    const validPassword = await usersService.comparePassword(request.person, oldPassword);
    if (!validPassword) throw request.server.errors.INVALID_CREDENTIALS;

    await usersService.updatePassword(id, newPassword);
    reply.sendJson();
  }

  async updateProfile(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.person;
    const { dob, gender, avatar, ...profileData } = request.body as UserDTOTypes['UpdateProfile'];

    const avatarKey = avatar ? await request.server.uploadImage(avatar) : undefined;

    const user = await usersService.updateProfileById(id, {
      ...profileData,
      avatarKey,
      gender: gender as Gender,
      dob: dob ? new Date(dob) : undefined,
    });

    reply.sendJson(serializePerson(user));
  }

  async updateResume(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.person;
    const { resume } = request.body as UserDTOTypes['UpdateResume'];

    const fileType = await fileTypeFromBuffer(resume);
    if (!fileType || !RESUME_MIME_TYPES.includes(fileType.mime))
      throw request.server.errors.FILE_TYPE_ERROR;

    const resumeKey = await request.server.uploadToMinio({
      buffer: resume,
      fileType,
      bucket: MinioBucket.RESUMES,
    });

    const user = await usersService.updateProfileById(id, { resumeKey });
    reply.sendJson(serializePerson(user));
  }

  async getUserById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as UserDTOTypes['GetUserById'];
    const user = await usersService.getById(id);
    if (!user) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializePerson(user));
  }

  async getUsers(request: FastifyRequest, reply: FastifyReply) {
    const { page, limit, role, search } = request.query as UserDTOTypes['GetUsers'];
    const result = await usersService.getList({ page, limit, role, search });
    reply.sendJson(
      result.persons.map((person) => serializePerson(person)),
      result.pagination,
    );
  }

  async createUser(request: FastifyRequest, reply: FastifyReply) {
    const { email, name, password, role } = request.body as UserDTOTypes['CreateUser'];
    const user = await usersService.create({ email, name, password, role: role as Role });
    reply.sendJson(serializePerson(user));
  }

  async updateUserActiveById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as UserDTOTypes['UpdateUserActiveParams'];
    const { active } = request.body as UserDTOTypes['UpdateUserActiveBody'];
    await usersService.updateActiveById(id, active);
    reply.sendJson();
  }

  async deleteUserById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as UserDTOTypes['DeleteUserById'];
    await usersService.deleteById(id);
    reply.sendJson();
  }
}

export default new UsersController();
