import fp from 'fastify-plugin';
import fastifyOAuth2 from '@fastify/oauth2';
import urlJoin from 'url-join';

const oauthPlugin = fp(async (app) => {
  const prefix = url<PERSON>oin(app.config.API_PREFIX, '/v1');
  const baseUrl = urlJoin(app.config.API_URL, prefix);

  app.register(fastifyOAuth2, {
    name: 'googleOAuth2',
    credentials: {
      client: {
        id: app.config.GOOGLE_CLIENT_ID,
        secret: app.config.GOOGLE_CLIENT_SECRET,
      },
      auth: fastifyOAuth2.GOOGLE_CONFIGURATION,
    },
    startRedirectPath: url<PERSON>oin(prefix, '/auth/google'),
    callbackUri: url<PERSON>oin(baseUrl, '/auth/callback/google'),
    schema: {
      summary: 'Redirect to Google OAuth2',
      description: 'Redirects the user to Google for OAuth2 authentication.',
      tags: ['auth'],
      security: [],
      response: { 302: { description: 'Redirect to Google OAuth2' } },
    },
  });
});

export default oauthPlugin;
