import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, SearchSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const SubscribeSchema = Type.Object({
  email: Type.String({ format: 'email', minLength: 1 }),
});

const UnsubscribeSchema = Type.Object({
  email: Type.String({ format: 'email', minLength: 1 }),
});

const GetEmailSubscribesSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
});

export const EmailDTO = {
  SubscribeSchema,
  UnsubscribeSchema,
  GetEmailSubscribesSchema,
};

export type EmailDTOTypes = StaticFromSchema<typeof EmailDTO>;
