import challengesService from './challenges.service';

import { FastifyRequest, FastifyReply } from 'fastify';
import { ChallengeDTOTypes } from './challenges.dto';
import { serializeChallenge } from '@/common/utils/serialize';

class ChallengesController {
  async createChallenge(request: FastifyRequest, reply: FastifyReply) {
    const { title, description, thumbnail, articleUrl } =
      request.body as ChallengeDTOTypes['CreateChallenge'];
    const thumbnailKey = await request.server.uploadImage(thumbnail);
    const challenge = await challengesService.create({
      title,
      description,
      thumbnailKey,
      articleUrl,
    });
    reply.sendJson(serializeChallenge(challenge));
  }

  async getChallengeById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ChallengeDTOTypes['GetChallenge'];
    const challenge = await challengesService.getById(id);
    if (!challenge) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializeChallenge(challenge));
  }

  async getChallenges(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search } = request.query as ChallengeDTOTypes['GetChallenges'];
    const { challenges, pagination } = await challengesService.getList({ limit, page, search });
    reply.sendJson(
      challenges.map((challenge) => serializeChallenge(challenge)),
      pagination,
    );
  }

  async updateChallenge(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ChallengeDTOTypes['UpdateChallengeParams'];
    const { title, description, thumbnail, articleUrl } =
      request.body as ChallengeDTOTypes['UpdateChallengeBody'];

    const oldChallenge = await challengesService.getById(id);
    if (!oldChallenge) throw request.server.errors.NOT_FOUND;

    const thumbnailKey = thumbnail ? await request.server.uploadImage(thumbnail) : undefined;
    const challenge = await challengesService.updateById(id, {
      title,
      description,
      thumbnailKey,
      articleUrl,
    });
    reply.sendJson(serializeChallenge(challenge));
  }

  async deleteChallenge(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ChallengeDTOTypes['DeleteChallenge'];
    const oldChallenge = await challengesService.getById(id);
    if (!oldChallenge) throw request.server.errors.NOT_FOUND;
    await challengesService.deleteById(id);
    reply.sendJson();
  }
}

export default new ChallengesController();
