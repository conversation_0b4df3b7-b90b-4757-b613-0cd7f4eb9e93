import { FastifyRequest, FastifyReply } from 'fastify';
import { ArticleDTOTypes } from './articles.dto';
import { serializeArticle } from '@/common/utils/serialize';
import { toFileUrl } from '@/common/utils/helpers';

import articlesService from './articles.service';

class ArticlesController {
  async createArticle(request: FastifyRequest, reply: FastifyReply) {
    const { content, thumbnail, redirectUrl, categoryId, ...data } =
      request.body as ArticleDTOTypes['CreateArticle'];

    if (!redirectUrl && !content) throw request.server.errors.BAD_REQUEST;

    const category = await articlesService.getCategoryById(categoryId);
    if (!category) throw request.server.errors.VALIDATION_ERROR;

    const thumbnailKey = await request.server.uploadImage(thumbnail);

    const article = await articlesService.create({
      ...data,
      content,
      thumbnailKey,
      authorId: request.person.id,
      redirectUrl,
      categoryId,
    });

    reply.sendJson(serializeArticle(article));
  }

  async updateArticle(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ArticleDTOTypes['UpdateArticleParams'];
    const { thumbnail, categoryId, ...data } = request.body as ArticleDTOTypes['UpdateArticleBody'];

    const oldArticle = await articlesService.getById(id);
    if (!oldArticle) throw request.server.errors.NOT_FOUND;

    if (categoryId) {
      const category = await articlesService.getCategoryById(categoryId);
      if (!category) throw request.server.errors.VALIDATION_ERROR;
    }

    const thumbnailKey = thumbnail ? await request.server.uploadImage(thumbnail) : undefined;

    const article = await articlesService.updateById(id, {
      ...data,
      thumbnailKey,
      categoryId,
    });

    reply.sendJson(serializeArticle(article));
  }

  async uploadImage(request: FastifyRequest, reply: FastifyReply) {
    const { image } = request.body as ArticleDTOTypes['UploadImage'];
    const key = await request.server.uploadImage(image);
    const imageUrl = toFileUrl(key);
    reply.sendJson({ imageUrl });
  }

  async getArticleBySlug(request: FastifyRequest, reply: FastifyReply) {
    const { slug } = request.params as ArticleDTOTypes['GetArticleBySlug'];
    const article = await articlesService.getBySlug(slug);
    if (!article) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializeArticle(article));
  }

  async getArticles(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search, categoryId } = request.query as ArticleDTOTypes['GetArticles'];

    const { articles, pagination } = await articlesService.getList({
      limit,
      page,
      search,
      categoryId,
    });

    reply.sendJson(
      articles.map((article) => serializeArticle(article)),
      pagination,
    );
  }

  async getArticleById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ArticleDTOTypes['GetArticleById'];
    const article = await articlesService.getById(id);
    if (!article) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializeArticle(article));
  }

  async deleteArticle(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ArticleDTOTypes['DeleteArticle'];
    const article = await articlesService.getById(id);
    if (!article) throw request.server.errors.NOT_FOUND;
    await articlesService.deleteById(id);
    reply.sendJson();
  }

  async createCategory(request: FastifyRequest, reply: FastifyReply) {
    const { name } = request.body as ArticleDTOTypes['CreateCategory'];
    const category = await articlesService.createCategory({ name });
    reply.sendJson(category);
  }

  async getCategories(request: FastifyRequest, reply: FastifyReply) {
    const categories = await articlesService.getCategories();
    reply.sendJson(categories);
  }

  async deleteCategory(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ArticleDTOTypes['DeleteCategory'];
    const category = await articlesService.getCategoryById(id);
    if (!category) throw request.server.errors.NOT_FOUND;
    await articlesService.deleteCategoryById(id);
    reply.sendJson();
  }
}

export default new ArticlesController();
