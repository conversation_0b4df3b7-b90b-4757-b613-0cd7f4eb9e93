import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma } from '@prisma/client';

class ChallengesService {
  async create(data: {
    title: string;
    description: string;
    thumbnailKey: string;
    articleUrl?: string;
  }) {
    return await withTransaction(async (tx) => {
      return await tx.challenge.create({ data });
    });
  }

  async updateById(
    id: number,
    data: {
      title?: string;
      description?: string;
      thumbnailKey?: string;
      articleUrl?: string;
    },
  ) {
    return await withTransaction(async (tx) => {
      return await tx.challenge.update({
        where: { id },
        data,
      });
    });
  }

  async getById(id: number) {
    return await prisma.challenge.findUnique({ where: { id } });
  }

  async getList(options: { limit?: number; page?: number; search?: string }) {
    const { limit = 10, page = 1, search } = options;

    const where: Prisma.ChallengeWhereInput = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [challenges, count] = await prisma.$transaction([
      prisma.challenge.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
      }),
      prisma.challenge.count({ where }),
    ]);

    return { challenges, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.challenge.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
}

export default new ChallengesService();
