import notificationsService from './notifications.service';

import { FastifyRequest, FastifyReply } from 'fastify';
import { NotificationDTOTypes } from './notifications.dto';

class NotificationsController {
  async subscribe(request: FastifyRequest, reply: FastifyReply) {
    const { endpoint, expirationTime, keys } = request.body as NotificationDTOTypes['Subscribe'];
    await notificationsService.subscribe({
      personId: request.person?.id || undefined,
      endpoint,
      expirationTime: expirationTime ? new Date(expirationTime) : undefined,
      p256dh: keys.p256dh,
      auth: keys.auth,
    });
    reply.sendJson();
  }

  async sendMessage(request: FastifyRequest, reply: FastifyReply) {
    const subs = await notificationsService.getSubscriptions();

    for (const sub of subs) {
      try {
        await notificationsService.sendMessage(sub);
      } catch (error) {
        request.server.log.error(error);
      }
    }

    reply.sendJson();
  }
}

export default new NotificationsController();
