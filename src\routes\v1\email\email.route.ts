import emailController from './email.controller';

import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { EmailDTO } from './email.dto';

export default async function jobsRoutes(app: FastifyInstance) {
  app.post('/email/subscribe', {
    handler: emailController.subscribe,
    schema: {
      summary: 'Subscribe to email',
      description: 'Subscribe to email',
      tags: ['email'],
      security: [],
      body: EmailDTO.SubscribeSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
        401: createErrorSchema(401),
      },
    },
  });

  app.post('/email/unsubscribe', {
    handler: emailController.unsubscribe,
    schema: {
      summary: 'Unsubscribe from email',
      description: 'Unsubscribe from email',
      tags: ['email'],
      security: [],
      body: EmailDTO.UnsubscribeSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
        401: createErrorSchema(401),
      },
    },
  });

  // Admin
  app.get('/admin/email/subscribes', {
    handler: emailController.getEmailSubscribes,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get list of email subscribes',
      description: 'Get list of email subscribes',
      tags: ['email', 'admin'],
      querystring: EmailDTO.GetEmailSubscribesSchema,
      response: {
        200: createSuccessSchema(Type.Array(Type.String()), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });
}
