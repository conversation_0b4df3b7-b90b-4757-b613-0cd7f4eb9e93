import fs from 'fs';
import path from 'path';
import fp from 'fastify-plugin';
import swagger from '@fastify/swagger';
import swaggerUI from '@fastify/swagger-ui';

const cssPath = path.resolve(process.cwd(), 'public/swagger.css');
const jsPath = path.resolve(process.cwd(), 'public/swagger.js');
const rapidocPath = path.resolve(process.cwd(), 'public/rapidoc.html');

const cssContent = fs.readFileSync(cssPath, 'utf-8');
const jsContent = fs.readFileSync(jsPath, 'utf-8');
const rapidocContent = fs.readFileSync(rapidocPath, 'utf-8');

const swaggerPlugin = fp(async (app) => {
  await app.register(swagger, {
    openapi: {
      openapi: '3.1.0',
      info: {
        title: app.config.API_NAME,
        version: app.config.API_VERSION,
        description: 'API documentation',
      },
      tags: [
        { name: 'auth', description: 'Authentication' },
        { name: 'email', description: 'Email' },
        { name: 'users', description: 'Users' },
        { name: 'quiz', description: 'Quiz' },
        { name: 'school', description: 'School' },
        { name: 'articles', description: 'Articles' },
        { name: 'company', description: 'Company' },
        { name: 'job', description: 'Job' },
        { name: 'challenge', description: 'Challenge' },
        { name: 'notification', description: 'Notification' },
        { name: 'admin', description: 'Admin' },
        { name: 'employer', description: 'Employer' },
      ],
      servers: [
        {
          url: app.config.API_URL,
          description:
            app.config.NODE_ENV === 'production' ? 'Production Server' : 'Development Server',
        },
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
      security: [{ bearerAuth: [] }],
    },
  });

  await app.register(swaggerUI, {
    routePrefix: '/api/swagger',
    uiConfig: {
      docExpansion: 'list',
      defaultModelsExpandDepth: -1,
      defaultModelExpandDepth: 0,
      deepLinking: false,
      persistAuthorization: true,
    },
    uiHooks: {
      onRequest: function (request, reply, next) {
        next();
      },
      preHandler: function (request, reply, next) {
        next();
      },
    },
    staticCSP: true,
    transformStaticCSP: (header) => header,
    transformSpecification: (swaggerObject) => {
      return swaggerObject;
    },
    transformSpecificationClone: true,
    logLevel: 'silent',
    theme: {
      css: [{ filename: 'theme.css', content: cssContent }],
      js: [{ filename: 'auth.js', content: jsContent }],
    },
  });

  app.get('/api/rapidoc', {
    schema: { hide: true },
    logLevel: 'silent',
    handler: async (req, reply) => {
      reply.type('text/html').send(rapidocContent);
    },
  });
});

export default swaggerPlugin;
