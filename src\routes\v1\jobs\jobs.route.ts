import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { JobsDTO } from './jobs.dto';
import { ModelSchemas } from '@/schema/model.schema';
import jobsController from './jobs.controller';

export default async function jobsRoutes(app: FastifyInstance) {
  app.get('/jobs', {
    handler: jobsController.getJobs,
    schema: {
      summary: 'Get jobs',
      description: 'Get jobs',
      tags: ['job'],
      security: [],
      querystring: JobsDTO.GetJobsSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.JobSchema), true),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/jobs/:id', {
    handler: jobsController.getJobById,
    schema: {
      summary: 'Get job by id',
      description: 'Get job by id',
      tags: ['job'],
      security: [],
      params: JobsDTO.GetJobSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.JobSchema),
        400: createErrorSchema(),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/jobs/apply', {
    handler: jobsController.applyJob,
    preHandler: [app.authenticate],
    schema: {
      summary: 'Apply for job',
      description: 'Apply for job',
      tags: ['job'],
      body: JobsDTO.ApplyJobSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  // Admin

  app.get('/admin/jobs', {
    handler: jobsController.getJobsForAdmin,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN, Role.EMPLOYER])],
    schema: {
      summary: 'Get jobs',
      description: 'Get jobs',
      tags: ['job', 'admin', 'employer'],
      querystring: JobsDTO.GetJobsSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.JobSchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        403: createErrorSchema(403),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/jobs/:id', {
    handler: jobsController.getJobByIdForAdmin,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN, Role.EMPLOYER])],
    schema: {
      summary: 'Get job by id',
      description: 'Get job by id',
      tags: ['job', 'admin', 'employer'],
      params: JobsDTO.GetJobSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.JobSchema),
        400: createErrorSchema(),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/jobs', {
    handler: jobsController.createJob,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN, Role.EMPLOYER])],
    schema: {
      summary: 'Create job',
      description: 'Create job',
      tags: ['job', 'admin', 'employer'],
      body: JobsDTO.CreateJobSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.JobSchema),
        400: createErrorSchema(1001),
        403: createErrorSchema(403),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.patch('/admin/jobs/:id', {
    handler: jobsController.updateJob,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN, Role.EMPLOYER])],
    schema: {
      summary: 'Update job information',
      description: 'Update job information',
      tags: ['job', 'admin', 'employer'],
      params: JobsDTO.UpdateJobParamsSchema,
      body: JobsDTO.UpdateJobBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.JobSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        403: createErrorSchema(403),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/jobs/:id', {
    handler: jobsController.deleteJob,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN, Role.EMPLOYER])],
    schema: {
      summary: 'Delete job',
      description: 'Delete job',
      tags: ['job', 'admin', 'employer'],
      params: JobsDTO.DeleteJobSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
