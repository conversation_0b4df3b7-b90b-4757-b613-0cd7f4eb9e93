import { FastifyRequest, FastifyReply } from 'fastify';
import { CompanyDTOTypes } from './companies.dto';
import { serializeCompany } from '@/common/utils/serialize';

import companiesService from './companies.service';

class CompaniesController {
  async createCompany(request: FastifyRequest, reply: FastifyReply) {
    const { employerName, employerEmail, employerPassword, logo, ...data } =
      request.body as CompanyDTOTypes['CreateCompany'];

    const logoKey = logo ? await request.server.uploadImage(logo) : undefined;
    const company = await companiesService.create(
      { ...data, logoKey },
      { name: employerName, email: employerEmail, password: employerPassword },
    );

    reply.sendJson(serializeCompany(company));
  }

  async getCompanyById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as CompanyDTOTypes['GetCompany'];
    const company = await companiesService.getById(id);
    if (!company) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializeCompany(company));
  }

  async getCompanies(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search } = request.query as CompanyDTOTypes['GetCompanies'];
    const { companies, pagination } = await companiesService.getList({ limit, page, search });
    reply.sendJson(
      companies.map((company) => serializeCompany(company)),
      pagination,
    );
  }

  async updateCompany(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as CompanyDTOTypes['UpdateCompanyParams'];
    const { logo, ...data } = request.body as CompanyDTOTypes['UpdateCompanyBody'];

    const oldCompany = await companiesService.getById(id);
    if (!oldCompany) throw request.server.errors.NOT_FOUND;

    const logoKey = logo ? await request.server.uploadImage(logo) : undefined;
    const company = await companiesService.updateById(id, { ...data, logoKey });

    reply.sendJson(serializeCompany(company));
  }

  async deleteCompany(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as CompanyDTOTypes['DeleteCompany'];
    const company = await companiesService.getById(id);
    if (!company) throw request.server.errors.NOT_FOUND;
    await companiesService.deleteById(id);
    reply.sendJson();
  }
}

export default new CompaniesController();
