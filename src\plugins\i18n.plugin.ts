import en from '../locales/en.json';
import vi from '../locales/vi.json';
import fp from 'fastify-plugin';

const locales = { en, vi };

type Language = keyof typeof locales;

function isLanguage(lang: string | undefined): lang is Language {
  if (!lang) return false;
  return lang in locales;
}

function getNestedValue(obj: any, keyPath: string): string | undefined {
  return keyPath.split('.').reduce((acc, key) => {
    if (typeof acc === 'object' && acc !== null && key in acc) {
      return acc[key];
    }
    return undefined;
  }, obj);
}

export function translate(key: string, lang: Language = 'en'): string {
  return getNestedValue(locales[lang], key) ?? getNestedValue(locales.en, key) ?? key;
}

const i18nPlugin = fp(async (app) => {
  app.addHook('onRequest', async (request) => {
    const rawLang = request.headers['accept-language']?.toLowerCase().slice(0, 2);
    const lang = isLanguage(rawLang) ? rawLang : 'en';
    request.i18n = { t: (key: string) => translate(key as any, lang), lang };
  });
});

export default i18nPlugin;
