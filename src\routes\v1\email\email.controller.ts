import emailService from './email.service';

import { FastifyRequest, FastifyReply } from 'fastify';
import { EmailDTOTypes } from './email.dto';

class EmailController {
  async subscribe(request: FastifyRequest, reply: FastifyReply) {
    const { email } = request.body as EmailDTOTypes['Subscribe'];
    await emailService.subscribe(email);
    reply.sendJson();
  }

  async unsubscribe(request: FastifyRequest, reply: FastifyReply) {
    const { email } = request.body as EmailDTOTypes['Unsubscribe'];
    await emailService.unsubscribe(email);
    reply.sendJson();
  }

  async getEmailSubscribes(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search } = request.query as EmailDTOTypes['GetEmailSubscribes'];
    const { emailSubscribes, pagination } = await emailService.getList({ limit, page, search });
    reply.sendJson(emailSubscribes, pagination);
  }
}

export default new EmailController();
