import { PinoLoggerOptions } from 'fastify/types/logger';
import fs from 'fs';
import path from 'path';

const logDir = path.join(process.cwd(), 'logs');
const logPath = path.join(process.cwd(), 'logs', 'app.log');
const errorLogPath = path.join(process.cwd(), 'logs', 'error.log');

if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

const pinoPlugin: PinoLoggerOptions = {
  transport: {
    targets: [
      {
        target: 'pino-pretty',
        options: {
          colorize: true,
          ignore: 'pid,hostname',
          translateTime: 'yyyy-mm-dd HH:MM:ss',
        },
      },
      {
        target: 'pino-pretty',
        options: {
          destination: logPath,
          colorize: false,
          ignore: 'pid,hostname',
          translateTime: 'yyyy-mm-dd HH:MM:ss',
        },
      },
      {
        target: 'pino-pretty',
        level: 'error',
        options: {
          destination: errorLogPath,
          colorize: false,
          ignore: 'pid,hostname',
          translateTime: 'yyyy-mm-dd HH:MM:ss',
        },
      },
    ],
  },
};

export default pinoPlugin;
