import { prisma } from '@/plugins/prisma.plugin';
import { webpush } from '@/plugins/webpush.plugin';
import { Subscription } from '@prisma/client';

class NotificationsService {
  async subscribe(data: {
    personId?: number;
    endpoint: string;
    expirationTime?: Date;
    p256dh: string;
    auth: string;
  }) {
    await prisma.subscription.create({
      data: { ...data },
    });
  }

  async getSubscriptions() {
    return await prisma.subscription.findMany();
  }

  async sendMessage(sub: Subscription) {
    const pushSub = {
      endpoint: sub.endpoint,
      expirationTime: sub.expirationTime?.getTime(),
      keys: {
        p256dh: sub.p256dh,
        auth: sub.auth,
      },
    };

    const payload = JSON.stringify({
      title: 'Xin chào!',
      body: '<PERSON><PERSON><PERSON> là thông báo từ hệ thống',
      icon: '/icon.png',
      url: 'https://google.com',
    });

    await webpush.sendNotification(pushSub, payload);
  }
}

export default new NotificationsService();
