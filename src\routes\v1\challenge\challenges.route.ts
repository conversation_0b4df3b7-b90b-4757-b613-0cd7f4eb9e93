import challengesController from './challenges.controller';

import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { ChallengesDTO } from './challenges.dto';
import { ModelSchemas } from '@/schema/model.schema';

export default async function quizzesRoutes(app: FastifyInstance) {
  app.get('/challenges', {
    handler: challengesController.getChallenges,
    schema: {
      summary: 'Get challenges',
      description: 'Get challenges',
      tags: ['challenge'],
      security: [],
      querystring: ChallengesDTO.GetChallengesSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.ChallengeSchema), true),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/challenges/:id', {
    handler: challengesController.getChallengeById,
    schema: {
      summary: 'Get challenge by id',
      description: 'Get challenge by id',
      tags: ['challenge'],
      security: [],
      params: ChallengesDTO.GetChallengeSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ChallengeSchema),
        400: createErrorSchema(),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/challenges', {
    handler: challengesController.getChallenges,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get challenges',
      description: 'Get challenges',
      tags: ['challenge', 'admin'],
      querystring: ChallengesDTO.GetChallengesSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.ChallengeSchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/challenges/:id', {
    handler: challengesController.getChallengeById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get challenge by id',
      description: 'Get challenge by id',
      tags: ['challenge', 'admin'],
      params: ChallengesDTO.GetChallengeSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ChallengeSchema),
        400: createErrorSchema(),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/challenges', {
    handler: challengesController.createChallenge,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create challenge',
      description: 'Create challenge',
      consumes: ['multipart/form-data'],
      tags: ['challenge', 'admin'],
      body: ChallengesDTO.CreateChallengeSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ChallengeSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.patch('/admin/challenges/:id', {
    handler: challengesController.updateChallenge,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update challenge information',
      description: 'Update challenge information',
      consumes: ['multipart/form-data'],
      tags: ['challenge', 'admin'],
      params: ChallengesDTO.UpdateChallengeParamsSchema,
      body: ChallengesDTO.UpdateChallengeBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ChallengeSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/challenges/:id', {
    handler: challengesController.deleteChallenge,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete challenge',
      description: 'Delete challenge',
      tags: ['challenge', 'admin'],
      params: ChallengesDTO.DeleteChallengeSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
