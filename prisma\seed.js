import { PrismaClient } from '@prisma/client';

async function main() {
  const prisma = new PrismaClient();

  await prisma.schoolType.createMany({
    data: [
      { name: 'Trường được AEFE công nhận' },
      { name: 'Trường LabelFrancÉducation' },
      { name: 'Đ<PERSON><PERSON> học giảng dạy tiếng Pháp hoặc bằng tiếng Pháp' },
      { name: 'Trung tâm đào tạo tiếng <PERSON>' },
      { name: 'Trung tâm tổ chức kỳ thi' },
    ],
  });
}

main();
