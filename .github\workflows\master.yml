name: Master Pipeline

on:
  push:
    branches:
      - master

env:
  REGISTRY_URL: registry.vuongninh.dev
  IMAGE_NAME: france-api
  TAG: latest

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to Docker Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY_URL }}           # ví dụ: docker.io
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and Push Docker Image
        run: |
          docker build -t ${{ env.REGISTRY_URL }}/${{ env.IMAGE_NAME }}:${{ env.TAG }} .
          docker push ${{ env.REGISTRY_URL }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}
