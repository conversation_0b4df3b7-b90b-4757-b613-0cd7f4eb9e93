import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, SearchSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const CreateSchoolSchema = Type.Object({
  name: Type.String({ minLength: 1 }),
  schoolTypeId: Type.Number(),
  city: Type.Optional(Type.String({ minLength: 1 })),
  address: Type.Optional(Type.String({ minLength: 1 })),
  description: Type.Optional(Type.String({ minLength: 1 })),
  website: Type.Optional(Type.String({ format: 'uri' })),
  latitude: Type.Optional(Type.Number()),
  longitude: Type.Optional(Type.Number()),
  logo: Type.Optional(Type.Any({ isFile: true })),
});

const UpdateSchoolParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateSchoolBodySchema = Type.Object({
  name: Type.Optional(Type.String({ minLength: 1 })),
  schoolTypeId: Type.Optional(Type.Number()),
  city: Type.Optional(Type.String({ minLength: 1 })),
  address: Type.Optional(Type.String({ minLength: 1 })),
  description: Type.Optional(Type.String({ minLength: 1 })),
  website: Type.Optional(Type.String({ format: 'uri' })),
  latitude: Type.Optional(Type.Number()),
  longitude: Type.Optional(Type.Number()),
  logo: Type.Optional(Type.Any({ isFile: true })),
});

const GetSchoolSchema = Type.Object({
  id: Type.Number(),
});

const GetSchoolsSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
  city: Type.Optional(Type.String({ minLength: 1 })),
  schoolTypeId: Type.Optional(Type.Number()),
});

const DeleteSchoolSchema = Type.Object({
  id: Type.Number(),
});

export const SchoolDTO = {
  CreateSchoolSchema,
  UpdateSchoolParamsSchema,
  UpdateSchoolBodySchema,
  GetSchoolSchema,
  GetSchoolsSchema,
  DeleteSchoolSchema,
};

export type SchoolDTOTypes = StaticFromSchema<typeof SchoolDTO>;
