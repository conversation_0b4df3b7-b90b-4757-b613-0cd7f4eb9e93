import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { AuthDTO } from './auth.dto';

import authController from './auth.controller';

export default async function authAdminRoutes(app: FastifyInstance) {
  app.post('/auth/register', {
    handler: authController.register,
    schema: {
      summary: 'Register account',
      description: 'Create a new user account.',
      tags: ['auth'],
      security: [],
      body: AuthDTO.RegisterSchema,
      response: {
        200: createSuccessSchema(AuthDTO.AuthResponseSchema),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/auth/login', {
    handler: authController.login,
    schema: {
      summary: 'Login account',
      description: 'Authenticate user and return access token.',
      tags: ['auth'],
      security: [],
      body: AuthDTO.LoginSchema,
      response: {
        200: createSuccessSchema(AuthDTO.AuthResponseSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(2001),
        403: createErrorSchema(403),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/auth/callback/google', {
    handler: authController.gooogleCallback,
    schema: {
      summary: 'Google OAuth2 Callback',
      description:
        'This endpoint is called by Google after the user authenticates and consents. It handles the OAuth2 authorization code exchange and returns an access token',
      tags: ['auth'],
      security: [],
      response: {
        200: createSuccessSchema(AuthDTO.AuthResponseSchema),
        500: createErrorSchema(500),
      },
    },
  });
}
