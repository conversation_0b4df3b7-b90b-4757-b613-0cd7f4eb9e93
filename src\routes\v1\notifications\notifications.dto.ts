import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Nullable } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const SubscribeSchema = Type.Object({
  endpoint: Type.String({ minLength: 1 }),
  expirationTime: Nullable(Type.String({ minLength: 1 })),
  keys: Type.Object({
    p256dh: Type.String({ minLength: 1 }),
    auth: Type.String({ minLength: 1 }),
  }),
});

export const NotificationsDTO = {
  SubscribeSchema,
};

export type NotificationDTOTypes = StaticFromSchema<typeof NotificationsDTO>;
