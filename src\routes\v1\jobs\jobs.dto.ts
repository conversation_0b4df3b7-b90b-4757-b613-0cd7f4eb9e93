import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, SearchSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const CreateJobSchema = Type.Object({
  title: Type.String({ minLength: 1 }),
  description: Type.String({ minLength: 1 }),
  requirements: Type.Array(Type.String({ minLength: 1 })),
  benefits: Type.Array(Type.String({ minLength: 1 })),
  salary: Type.String({ minLength: 1 }),
  companyId: Type.Number(),
  tags: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
});

const UpdateJobParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateJobBodySchema = Type.Object({
  title: Type.Optional(Type.String({ minLength: 1 })),
  description: Type.Optional(Type.String({ minLength: 1 })),
  requirements: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
  benefits: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
  salary: Type.Optional(Type.String({ minLength: 1 })),
  tags: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
});

const GetJobSchema = Type.Object({
  id: Type.Number(),
});

const GetJobsSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
  companyId: Type.Optional(Type.Number()),
  tag: Type.Optional(Type.String({ minLength: 1 })),
});

const DeleteJobSchema = Type.Object({
  id: Type.Number(),
});

const ApplyJobSchema = Type.Object({
  jobId: Type.Number(),
});

export const JobsDTO = {
  CreateJobSchema,
  UpdateJobParamsSchema,
  UpdateJobBodySchema,
  GetJobSchema,
  GetJobsSchema,
  DeleteJobSchema,
  ApplyJobSchema,
};

export type JobDTOTypes = StaticFromSchema<typeof JobsDTO>;
