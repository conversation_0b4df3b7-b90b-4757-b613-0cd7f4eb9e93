import usersController from './users.controller';

import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { UsersDTO } from './users.dto';
import { Role } from '@prisma/client';
import { ModelSchemas } from '@/schema/model.schema';

export default async function usersRoutes(app: FastifyInstance) {
  app.get('/users/me', {
    handler: usersController.getMe,
    preHandler: [app.authenticate],
    schema: {
      summary: 'Get current user',
      tags: ['users'],
      response: {
        200: createSuccessSchema(ModelSchemas.UserSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.put('/users/update/password', {
    handler: usersController.updatePassword,
    preHandler: [app.authenticate],
    schema: {
      summary: 'Update password',
      body: UsersDTO.UpdatePasswordSchema,
      tags: ['users'],
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.patch('/users/update/profile', {
    handler: usersController.updateProfile,
    preHandler: [app.authenticate],
    schema: {
      summary: 'Update profile',
      body: UsersDTO.UpdateProfileSchema,
      tags: ['users'],
      consumes: ['multipart/form-data'],
      response: {
        200: createSuccessSchema(ModelSchemas.UserSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.put('/users/update/resume', {
    handler: usersController.updateResume,
    preHandler: [app.authenticate],
    schema: {
      summary: 'Update resume',
      body: UsersDTO.UpdateResumeSchema,
      tags: ['users'],
      consumes: ['multipart/form-data'],
      response: {
        200: createSuccessSchema(ModelSchemas.UserSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  // Admin

  app.get('/admin/users', {
    handler: usersController.getUsers,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get list of users',
      description:
        'Retrieve a paginated list of users. Optional filters like role, email, etc. can be applied.',
      tags: ['users', 'admin'],
      querystring: UsersDTO.GetUsersSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.UserSchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/users/:id', {
    handler: usersController.getUserById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get user by ID',
      description:
        'Returns detail of a user by ID. Returns 404 if the user does not exist or was soft-deleted.',
      tags: ['users', 'admin'],
      params: UsersDTO.GetUserByIdSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.UserSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/users', {
    handler: usersController.createUser,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create a new user',
      description: 'Creates a new user with required fields.',
      tags: ['users', 'admin'],
      body: UsersDTO.CreateUserSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.UserSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/users/:id', {
    handler: usersController.deleteUserById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Soft delete a user',
      description:
        'Marks a user as deleted by setting deletedAt timestamp. The user will be excluded from future queries.',
      tags: ['users', 'admin'],
      params: UsersDTO.DeleteUserByIdSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.put('/admin/users/:id/active', {
    handler: usersController.updateUserActiveById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update user active status',
      description: 'Update user active status',
      tags: ['users', 'admin'],
      params: UsersDTO.UpdateUserActiveParamsSchema,
      body: UsersDTO.UpdateUserActiveBodySchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
