import { TypeBoxTypeProvider } from '@fastify/type-provider-typebox';

import Fastify from 'fastify';
import ajvCompiler from '@fastify/ajv-compiler';
import mainRoutes from './routes/main.route';

import cookiePlugin from './plugins/cookie.plugin';
import corsPlugin from './plugins/cors.plugin';
import envPlugin from './plugins/env.plugin';
import errorsPlugin from './plugins/errors.plugin';
import i18nPlugin from './plugins/i18n.plugin';
import jwtPlugin from './plugins/jwt.plugin';
import minioPlugin from './plugins/minio.plugin';
import multipartPlugin from './plugins/multipart.plugin';
import oauthPlugin from './plugins/oauth.plugin';
import pinoPlugin from './plugins/pino.plugin';
import prismaPlugin from './plugins/prisma.plugin';
import responsePlugin from './plugins/response.plugin';
import staticPlugin from './plugins/static.plugin';
import swaggerPlugin from './plugins/swagger.plugin';
import ajvPlugin from './plugins/ajvPlugin';
import webpushPlugin from './plugins/webpush.plugin';

export async function startServer() {
  const app = Fastify({
    logger: pinoPlugin,
    ajv: ajvPlugin,
    schemaController: {
      compilersFactory: {
        buildValidator: ajvCompiler(),
      },
    },
  }).withTypeProvider<TypeBoxTypeProvider>();

  await app.register(envPlugin);
  await app.register(corsPlugin);
  await app.register(cookiePlugin);
  await app.register(multipartPlugin);

  await app.register(prismaPlugin);
  await app.register(minioPlugin);
  await app.register(i18nPlugin);
  await app.register(staticPlugin);
  await app.register(webpushPlugin);

  await app.register(swaggerPlugin);
  await app.register(errorsPlugin);
  await app.register(jwtPlugin);
  await app.register(responsePlugin);
  await app.register(oauthPlugin);

  await app.register(mainRoutes, { prefix: app.config.API_PREFIX });
  await app.listen({ port: app.config.API_PORT, host: app.config.API_HOST });
}
