import { PrismaClient } from '@prisma/client';
import { Client } from 'minio';
import { v4 as uuidv4 } from 'uuid';
import { fileTypeFromBuffer } from 'file-type';

import fs from 'fs';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

dotenv.config();

const minioClient = new Client({
  endPoint: process.env.MINIO_ENDPOINT!,
  port: Number(process.env.MINIO_PORT!),
  useSSL: false,
  accessKey: process.env.MINIO_ACCESS_KEY!,
  secretKey: process.env.MINIO_SECRET_KEY!,
});

const prisma = new PrismaClient();

const genderOptions = ['MALE', 'FEMALE', 'OTHER'];

function random(arr: any[]) {
  return arr[Math.floor(Math.random() * arr.length)];
}

function range(n: number) {
  return Array.from({ length: n }, (_, i) => i + 1);
}

function rangeRandom(n: number) {
  const count = Math.floor(Math.random() * n) + 1;
  return Array.from({ length: count }, (_, i) => i + 1);
}

function pickRandom(arr: any[], count: number = 0) {
  if (count === 0) count = Math.floor(Math.random() * arr.length);
  const shuffled = [...arr].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

async function uploadToMinio(localFilePath: string, bucketName: string) {
  const fileBuffer = fs.readFileSync(localFilePath);
  const filetype = await fileTypeFromBuffer(fileBuffer);
  if (!filetype) throw new Error('File type not supported');
  const objectName = uuidv4() + '.' + filetype.ext;
  await minioClient.putObject(bucketName, objectName, fileBuffer);
  console.log('Successfully uploaded to Minio');
  return bucketName + '/' + objectName;
}

async function main() {
  const BUCKET_NAME = 'images';

  const exists = await minioClient.bucketExists(BUCKET_NAME);
  if (!exists) await minioClient.makeBucket(BUCKET_NAME);

  await minioClient.setBucketPolicy(
    BUCKET_NAME,
    JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        {
          Effect: 'Allow',
          Principal: '*',
          Action: 's3:GetObject',
          Resource: `arn:aws:s3:::images/*`,
        },
      ],
    }),
  );

  const images: string[] = [];
  for (const file of fs.readdirSync(process.cwd() + '/prisma/assets')) {
    const key = await uploadToMinio(process.cwd() + '/prisma/assets/' + file, BUCKET_NAME);
    images.push(key);
  }

  prisma.$transaction(async (tx) => {
    const password = await bcrypt.hash('Ab@12345', 10);

    await tx.person.createMany({
      skipDuplicates: true,
      data: range(100).map((i) => ({
        name: `User ${i}`,
        email: `user${i}@example.com`,
        password: password,
        role: 'USER',
      })),
    });

    // Users
    const users = await tx.person.findMany();

    for (const [index, user] of users.entries()) {
      await tx.profile.create({
        data: {
          personId: user.id,
          city: random(['Hà Nội', 'Đà Nẵng', 'Hồ Chí Minh']),
          dob: new Date(`199${index % 10}-01-01`),
          gender: random(genderOptions),
          frenchLevel: 'B2',
          expertiseDomains: ['AI', 'Web'],
          otherLanguages: ['English', 'Vietnamese'],
        },
      });
    }

    // Create admin
    const admin = await tx.person.create({
      data: {
        name: 'Admin',
        email: '<EMAIL>',
        password,
        role: 'ADMIN',
        profile: { create: {} },
      },
    });

    // School Types
    await tx.schoolType.createMany({
      data: [
        'Trường được AEFE công nhận',
        'Trường LabelFrancÉducation',
        'Đại học giảng dạy tiếng Pháp hoặc bằng tiếng Pháp',
        'Trung tâm đào tạo tiếng Pháp',
        'Trung tâm tổ chức kỳ thi',
      ].map((name) => ({ name })),
      skipDuplicates: true,
    });

    // Schools
    const schoolTypes = await tx.schoolType.findMany();
    await tx.school.createMany({
      data: range(20).map((i) => ({
        name: `Trường đại học Hà Nội cơ sở ${i}`,
        city: `Hà Nội`,
        address: `${i} Trần Hưng Đạo, Hàng Bài, Hoàn Kiếm, Hà Nội`,
        schoolTypeId: random(schoolTypes).id,
      })),
      skipDuplicates: true,
    });

    // Company Types
    await tx.companyType.createMany({
      data: [
        'Thú ý & Nông nghiệp',
        'Xây dựng',
        'Thời trang',
        'Thực phẩm & Đồ uống',
        'Văn hóa và thể thao',
        'Kinh doanh & Dịch vụ',
        'Công nghệ',
        'Du lịch',
        'Bất động sản',
        'Tài chính & Chứng khoán',
      ].map((name) => ({ name })),
      skipDuplicates: true,
    });

    // Companies
    const companyTypes = await tx.companyType.findMany();
    await tx.company.createMany({
      data: range(20).map((i) => ({
        name: `Công ty ${i}`,
        address: `${i} Trần Hưng Đạo, Hàng Bài, Hoàn Kiếm, Hà Nội`,
        videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        description: random([
          `Cap Education là đơn vị đồng hành đáng tin cậy của hàng nghìn học sinh, sinh viên Việt Nam trên hành trình du học Pháp. Với đội ngũ chuyên viên giàu kinh nghiệm, công ty luôn cam kết mang đến những giải pháp tư vấn hiệu quả và thiết thực nhất.`,
          `Trải qua nhiều năm hoạt động trong lĩnh vực tư vấn du học Pháp, Cap Education đã xây dựng được mạng lưới đối tác rộng khắp với các trường đại học, cao đẳng và trung tâm đào tạo uy tín tại Pháp.`,
          `Cap Education không chỉ hỗ trợ học sinh trong quá trình chuẩn bị hồ sơ và xin visa mà còn đồng hành cùng các bạn trong suốt quá trình học tập và sinh sống tại Pháp.`,
          `Với phương châm “Du học là con đường thay đổi tương lai”, Cap Education luôn đặt lợi ích và sự phát triển lâu dài của học sinh lên hàng đầu.`,
          `Cap Education cung cấp dịch vụ tư vấn toàn diện từ chọn trường, ngành học, luyện phỏng vấn visa đến hỗ trợ sinh hoạt tại Pháp, giúp học sinh yên tâm chinh phục giấc mơ du học.`,
          `Cap Education tự hào là cầu nối vững chắc giữa học sinh Việt Nam và nền giáo dục Pháp, mang lại cơ hội học tập quốc tế với chi phí hợp lý và lộ trình rõ ràng.`,
        ]),
        website: `https://company${i}.com`,
        companyTypeId: random(companyTypes).id,
        services: pickRandom([
          'Tư vấn lộ trình du học Pháp từ A-Z',
          'Đăng ký nhập học, thủ tục nhập học',
          'Đăng ký học bổng, hỗ trợ tài chính',
          'Đăng ký visa du học, thủ tục nhập cảnh',
          'Đăng ký học phí, hỗ trợ tài chính',
          'Đăng ký học phí, hỗ trợ tài chính',
          'Tư vấn lộ trình du học Pháp từ A-Z',
          'Hướng dẫn thủ tục hồ sơ xin visa du học Pháp',
          'Tư vấn chọn trường phù hợp theo năng lực và ngành học',
          'Dạy tiếng Pháp chuẩn bị cho kỳ thi TCF, DELF, DALF',
          'Tư vấn định hướng nghề nghiệp sau tốt nghiệp',
          'Hỗ trợ tìm nhà ở, bảo hiểm, các dịch vụ hỗ trợ khác tại Pháp',
          'Tổ chức hội thảo du học định kỳ, gặp gỡ trực tiếp với đại diện các trường tại Pháp',
          'Hướng dẫn và luyện phỏng vấn xin visa du học hiệu quả',
          'Hướng dẫn viết motivation letter và CV chuẩn Pháp',
          'Hỗ trợ chứng minh tài chính đúng chuẩn yêu cầu của Lãnh sự quán',
          'Tư vấn chuyển ngành, chuyển trường khi đang học tại Pháp',
          'Tư vấn bảo lưu kết quả học tập và xử lý các tình huống phát sinh tại Pháp',
          'Hướng dẫn làm quen với văn hóa và cuộc sống tại Pháp',
          'Hỗ trợ dịch thuật và công chứng hồ sơ học tập',
          'Tư vấn chọn ngành học phù hợp với xu hướng việc làm tại Pháp và toàn cầu',
        ]),
        highlights: pickRandom([
          'Đội ngũ tư vấn viên giàu kinh nghiệm, nhiều người từng học tập và sinh sống tại Pháp',
          'Chương trình luyện thi tiếng Pháp chất lượng cao, được thiết kế riêng cho mục tiêu du học',
          'Tỷ lệ đậu visa Pháp cao, được nhiều phụ huynh và học sinh tin tưởng',
          'Mối quan hệ hợp tác với nhiều trường đại học, trường ngôn ngữ, và tổ chức giáo dục uy tín tại Pháp',
          'Cam kết hỗ trợ 24/7, sẵn sàng giải đáp mọi thắc mắc của học sinh và phụ huynh',
          'Đội ngũ pháp lý và chuyên viên visa hỗ trợ tận tình từng trường hợp cụ thể',
          'Phản hồi nhanh chóng, cam kết xử lý hồ sơ đúng hạn',
          'Chi phí minh bạch, không phát sinh trong quá trình làm hồ sơ',
          'Cung cấp tài liệu học tiếng Pháp miễn phí và các buổi học thử',
          'Theo sát học sinh ngay cả khi đã sang Pháp học tập',
          'Hồ sơ được kiểm tra nhiều vòng đảm bảo độ chính xác tuyệt đối',
          'Chương trình hỗ trợ học sinh ở tỉnh lẻ hoặc không có điều kiện tài chính mạnh',
          'Tư vấn cá nhân hoá dựa trên mục tiêu học tập và tài chính của từng học sinh',
          'Chính sách hoàn phí minh bạch nếu không đạt visa du học',
        ]),
      })),
      skipDuplicates: true,
    });

    // Jobs
    const companies = await tx.company.findMany();
    for (const company of companies) {
      await tx.person.create({
        data: {
          name: `Employer of ${company.name}`,
          email: `employer${company.id}@example.com`,
          password,
          role: 'EMPLOYER',
          employer: { create: { companyId: company.id } },
          profile: { create: {} },
        },
      });

      await tx.job.createMany({
        data: rangeRandom(5).map(() => ({
          title: random([
            'Giáo viên Tiếng Pháp theo Hợp đồng',
            'Giáo viên Tiếng Pháp theo thời vụ',
            'Nhân viên bán hàng',
            'Nhân viên tư vấn du học',
            'Nhân viên chăm sóc khách hàng',
            'Nhân viên marketing',
            'Nhân viên truyền thông',
            'Nhân viên thiết kế',
            'Nhân viên phát triển sản phẩm',
            'Nhân viên kiểm tra chất lượng',
            'Nhân viên kỹ thuật',
            'Nhân viên tài chính',
            'Nhân viên hành chính',
            'Nhân viên nhân sự',
            'Nhân viên bán hàng online',
          ]),
          description: random([
            `Học viện Pháp tại Việt Nam đang tìm kiếm giáo viên tiếng Pháp để giảng dạy cho trẻ em, thiếu niên và người lớn.`,
            `Công ty chúng tôi đang tìm kiếm nhân viên bán hàng để tham gia vào đội ngũ bán hàng của chúng tôi.`,
            `Công ty chúng tôi đang tìm kiếm nhân viên tư vấn du học để tham gia vào đội ngũ tư vấn của chúng tôi.`,
            `Công ty chúng tôi đang tìm kiếm nhân viên chăm sóc khách hàng để tham gia vào đội ngũ chăm sóc khách hàng của chúng tôi.`,
            `Công ty chúng tôi đang tìm kiếm nhân viên marketing để tham gia vào đội ngũ marketing của chúng tôi.`,
          ]),
          requirements: pickRandom([
            'Ít nhất 1-2 năm kinh nghiệm trong lĩnh vực sale sản phẩm cao cấp, telesale, du lịch khách sạn.',
            'Độ tuổi: 24 - 32, ngoại hình ưa nhìn.',
            'Tiếng Anh cơ bản, giao tiếp tự tin, trách nhiệm cao, thái độ cầu thị.',
            'Gu thẩm mỹ tốt, có khả năng sáng tạo nội dung media.',
            'Kinh nghiệm trong lĩnh vực thiết kế đồ họa, marketing, truyền thông.',
            'Kinh nghiệm trong lĩnh vực kỹ thuật, phát triển sản phẩm.',
            'Kinh nghiệm trong lĩnh vực kiểm tra chất lượng, đảm bảo chất lượng sản phẩm.',
            'Kinh nghiệm trong lĩnh vực tài chính, quản lý tài chính của công ty.',
            'Kinh nghiệm trong lĩnh vực hành chính, quản lý hành chính của công ty.',
            'Kinh nghiệm trong lĩnh vực nhân sự, quản lý nhân sự của công ty.',
          ]),
          benefits: pickRandom([
            'Trực tiếp làm việc với các sản phẩm đồng hồ và phụ kiện xa xỉ từ các thương hiệu hàng đầu như Rapport, Swisskubik, Monte Grappa, XOR,...',
            'Đại diện cho Frodo’s tư vấn, phục vụ khách hàng, duy trì không gian sang trọng và xây dựng hình ảnh chuyên nghiệp trên social media.',
            'Phát triển tệp khách hàng VIP: doanh nhân, người có tầm ảnh hưởng, giới thượng lưu.',
            'Tham gia các buổi đào tạo nâng cao kỹ năng tư duy bán hàng và xây dựng hình ảnh cá nhân.',
            'Hưởng thưởng hấp dẫn: lương cạnh tranh, thưởng bán hàng hấp dẫn, chế độ bảo hiểm, hỗ trợ học tập, hỗ trợ mua bảo hiểm y tế, hỗ trợ mua nhà, hỗ trợ mua xe, hỗ trợ xây dựng nhà ở, hỗ trợ mua xe, hỗ trợ xây dựng nhà ở, hỗ trợ mua xe, hỗ trợ xây dựng nhà ở, hỗ trợ mua xe, hỗ trợ xây dựng nhà ở, hỗ trợ m',
          ]),
          salary: random([
            '$60,000 - $80,000 / năm',
            '$80,000 - $100,000 / năm',
            '$100,000 - $120,000 / năm',
            '$120,000 - $140,000 / năm',
            '$140,000 - $160,000 / năm',
          ]),
          companyId: company.id,
        })),
        skipDuplicates: true,
      });
    }

    // Challenges
    await tx.challenge.createMany({
      data: [
        {
          title: 'Tôi nói tiếng Pháp, còn bạn?',
          description:
            '🎯 Bạn đọc một đoạn hội thoại siêu đơn giản (tự giới thiệu, đi mua bánh mì…)\n👉 Mời người xem đóng vai người còn lại (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Đi chợ mua rau!',
          description:
            '🛒 Bạn là người mua hàng, đọc đoạn hội thoại đơn giản với người bán.\n👉 Mời người xem đóng vai người bán (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Đặt bàn trong nhà hàng',
          description:
            '🍽️ Bạn gọi điện đặt bàn ăn tại nhà hàng Pháp.\n👉 Mời người xem đóng vai nhân viên lễ tân (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Hỏi đường ở Paris',
          description:
            '🗺️ Bạn bị lạc và cần hỏi đường bằng tiếng Pháp.\n👉 Mời người xem đóng vai người dân địa phương (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Giới thiệu bản thân',
          description:
            '🙋 Bạn nói tên, tuổi, nghề nghiệp, sở thích bằng tiếng Pháp.\n👉 Người xem có thể phản hồi bằng cách giới thiệu bản thân họ.',
          thumbnailKey: random(images),
        },
        {
          title: 'Mua bánh sừng bò',
          description:
            '🥐 Bạn đến tiệm bánh hỏi mua croissant và pain au chocolat.\n👉 Người xem đóng vai người bán hàng (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Tôi bị đau bụng!',
          description:
            '🤒 Bạn mô tả triệu chứng bằng tiếng Pháp để gặp bác sĩ.\n👉 Người xem đóng vai bác sĩ (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Tôi thích cà phê sữa',
          description:
            '☕ Bạn nói về loại đồ uống yêu thích khi gọi món ở quán café.\n👉 Người xem đóng vai nhân viên phục vụ (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Phỏng vấn xin việc',
          description:
            '🧑‍💼 Bạn trả lời các câu hỏi phỏng vấn đơn giản bằng tiếng Pháp.\n👉 Người xem đóng vai nhà tuyển dụng (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Tôi học tiếng Pháp như thế nào?',
          description:
            '📚 Bạn chia sẻ lý do học tiếng Pháp và phương pháp học của mình.\n👉 Mời người xem phản hồi bằng câu chuyện của họ.',
          thumbnailKey: random(images),
        },
        {
          title: 'Đi taxi ở Pháp',
          description:
            '🚕 Bạn mô tả điểm đến cho tài xế taxi bằng tiếng Pháp.\n👉 Người xem đóng vai tài xế (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Mua vé tàu',
          description:
            '🚆 Bạn muốn mua vé tàu đi Lyon tại ga.\n👉 Người xem đóng vai nhân viên bán vé (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Bạn học lớp mấy?',
          description:
            '🏫 Bạn hỏi và trả lời về lớp học, trường, thầy cô bằng tiếng Pháp.\n👉 Người xem phản hồi bằng câu chuyện tương tự.',
          thumbnailKey: random(images),
        },
        {
          title: 'Chọn quà sinh nhật',
          description:
            '🎁 Bạn hỏi ý kiến để chọn một món quà sinh nhật cho bạn mình.\n👉 Người xem đóng vai nhân viên tư vấn (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Tôi bị mất điện thoại!',
          description:
            '📱 Bạn đến đồn cảnh sát để trình báo mất đồ bằng tiếng Pháp.\n👉 Người xem đóng vai cảnh sát (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Trò chuyện với bạn mới',
          description:
            '😊 Bạn làm quen và đặt các câu hỏi cơ bản bằng tiếng Pháp.\n👉 Người xem trả lời như một người bạn mới (duet).',
          thumbnailKey: random(images),
        },
        {
          title: 'Tôi đang học nấu ăn',
          description:
            '🍳 Bạn chia sẻ về món ăn bạn đang học và nguyên liệu cần có.\n👉 Người xem phản hồi bằng công thức của họ.',
          thumbnailKey: random(images),
        },
        {
          title: 'Mô tả phòng ngủ của bạn',
          description:
            '🛏️ Bạn mô tả phòng ngủ bằng tiếng Pháp (màu sắc, đồ vật…).\n👉 Người xem mô tả phòng của họ để duet.',
          thumbnailKey: random(images),
        },
        {
          title: 'Tôi yêu mùa thu',
          description:
            '🍂 Bạn nói về mùa yêu thích, lý do bạn thích mùa thu.\n👉 Người xem duet bằng cách chia sẻ mùa yêu thích của họ.',
          thumbnailKey: random(images),
        },
        {
          title: 'Một ngày của tôi',
          description:
            '🕒 Bạn kể về thói quen hàng ngày bằng tiếng Pháp.\n👉 Người xem phản hồi bằng lịch trình của họ.',
          thumbnailKey: random(images),
        },
      ],
      skipDuplicates: true,
    });

    // Quizzes
    await tx.quiz.createMany({
      data: [
        {
          title: 'Tiếng Pháp cơ bản',
          week: 1,
          description:
            'Hãy kiểm tra kiến thức của bạn về những cụm từ tiếng Pháp thiết yếu và từ vựng hàng ngày nhé!',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Chào hỏi và giới thiệu bản thân',
          week: 2,
          description:
            'Kiểm tra khả năng chào hỏi, giới thiệu tên tuổi, nghề nghiệp bằng tiếng Pháp.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Số đếm và ngày tháng',
          week: 3,
          description:
            'Bạn có nhớ cách nói số, ngày, tháng và năm trong tiếng Pháp không? Cùng kiểm tra nào!',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Gia đình và bạn bè',
          week: 4,
          description: 'Từ vựng về gia đình, mối quan hệ và cách mô tả người thân.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Màu sắc và quần áo',
          week: 5,
          description: 'Học từ vựng về màu sắc, trang phục và cách mô tả ngoại hình.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Thời tiết và mùa trong năm',
          week: 6,
          description: 'Kiểm tra hiểu biết về cách nói thời tiết và các mùa trong tiếng Pháp.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Thức ăn và đồ uống',
          week: 7,
          description: 'Cùng ôn lại từ vựng và mẫu câu thường dùng khi ăn uống ở Pháp.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Động từ cơ bản - Être và Avoir',
          week: 8,
          description: 'Bạn có thực sự nắm chắc hai động từ quan trọng nhất tiếng Pháp không?',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Đặt câu hỏi đơn giản',
          week: 9,
          description: 'Cùng thực hành cách đặt câu hỏi và trả lời trong tiếng Pháp.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Các thì hiện tại - Présent',
          week: 10,
          description: 'Ôn tập thì hiện tại đơn trong tiếng Pháp với các động từ thông dụng.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Phương tiện giao thông và di chuyển',
          week: 11,
          description: 'Từ vựng về xe cộ, phương hướng, và cách hỏi đường bằng tiếng Pháp.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Tính từ và mô tả',
          week: 12,
          description: 'Thử thách khả năng dùng tính từ để mô tả người, vật, và nơi chốn.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Hoạt động hàng ngày',
          week: 13,
          description: 'Kiểm tra từ vựng và cách diễn đạt về các hoạt động thường ngày.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Thì quá khứ - Passé Composé',
          week: 14,
          description:
            'Bạn có biết kể chuyện hoặc nói về một trải nghiệm trong quá khứ bằng tiếng Pháp?',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Từ nối và liên kết câu',
          week: 15,
          description: 'Cùng kiểm tra kỹ năng dùng từ nối để liên kết ý tưởng trong câu.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Tình huống trong nhà hàng',
          week: 16,
          description: 'Bạn sẽ làm gì khi đi ăn nhà hàng Pháp? Kiểm tra vốn từ và hội thoại!',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Cảm xúc và tâm trạng',
          week: 17,
          description: 'Học cách diễn tả cảm xúc như vui, buồn, lo lắng bằng tiếng Pháp.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Nghề nghiệp và công việc',
          week: 18,
          description: 'Từ vựng về công việc, mô tả nghề nghiệp, nơi làm việc, và kỹ năng.',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Tình huống khẩn cấp',
          week: 19,
          description:
            'Bạn có biết gọi cấp cứu, báo mất đồ hoặc xin giúp đỡ bằng tiếng Pháp không?',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
        {
          title: 'Bài kiểm tra tổng hợp A1',
          week: 20,
          description:
            'Tổng hợp kiến thức từ các tuần trước – bạn đã sẵn sàng để vượt qua trình độ A1 chưa?',
          kahootUrl: 'https://ka.ht/tvdvb0',
        },
      ],
      skipDuplicates: true,
    });

    // Category
    const categoryNames = ['Tech', 'Design', 'Business', 'Health', 'Lifestyle', 'Education'];
    await tx.category.createMany({
      data: categoryNames.map((name) => ({ name })),
      skipDuplicates: true,
    });

    // Articles
    const categories = await tx.category.findMany();
    await tx.article.createMany({
      data: [
        {
          title: 'Tại sao nên học tiếng Pháp?',
          summary: 'Tiếng Pháp là ngôn ngữ toàn cầu, mang lại nhiều cơ hội học tập và nghề nghiệp.',
          slug: 'tai-sao-nen-hoc-tieng-phap',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Tiếng Pháp là ngôn ngữ chính thức tại hơn 29 quốc gia.
      2. Là ngôn ngữ của ngoại giao và các tổ chức quốc tế.
      3. Mở rộng cơ hội học tập tại Pháp và các nước nói tiếng Pháp.
      4. Giúp khám phá văn hóa, ẩm thực và nghệ thuật Pháp.
      5. Là ngôn ngữ dễ học với nền tảng từ tiếng Anh.
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Các lỗi thường gặp khi học tiếng Pháp',
          summary: 'Tránh những lỗi phổ biến sẽ giúp bạn tiến bộ nhanh hơn.',
          slug: 'cac-loi-thuong-gap-khi-hoc-tieng-phap',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Phát âm không đúng vì không luyện nghe đủ.
      2. Dịch từng từ thay vì hiểu cả câu.
      3. Không luyện nói thường xuyên.
      4. Ngại sai nên không dám thực hành.
      5. Bỏ qua ngữ pháp căn bản.
      `,
          thumbnailKey: random(images),
        },
        {
          title: '5 ứng dụng học tiếng Pháp miễn phí',
          summary: 'Bạn có thể bắt đầu học tiếng Pháp ngay từ điện thoại của mình.',
          slug: '5-ung-dung-hoc-tieng-phap-mien-phi',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Duolingo – Học tiếng Pháp cơ bản thông qua trò chơi.
      2. Memrise – Học từ vựng bằng flashcard.
      3. LingQ – Nghe đọc tài liệu thực tế.
      4. HelloTalk – Trò chuyện với người bản ngữ.
      5. TV5MONDE – Học tiếng Pháp qua truyền hình.
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Học tiếng Pháp qua bài hát',
          summary: 'Âm nhạc giúp ghi nhớ từ vựng và cải thiện phát âm hiệu quả.',
          slug: 'hoc-tieng-phap-qua-bai-hat',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Chọn các bài hát có lời rõ ràng và đơn giản.
      2. Nghe nhiều lần và hát theo.
      3. Ghi chú từ mới và tra nghĩa.
      4. Luyện phát âm theo lời bài hát.
      `,
          thumbnailKey: random(images),
        },
        {
          title: '10 từ vựng tiếng Pháp mỗi ngày',
          summary: 'Chỉ 10 từ mỗi ngày, bạn sẽ biết hơn 3.000 từ trong 1 năm!',
          slug: '10-tu-vung-tieng-phap-moi-ngay',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Đặt mục tiêu mỗi ngày học 10 từ.
      2. Ôn lại vào cuối tuần.
      3. Dùng flashcard để ghi nhớ.
      4. Đặt câu với từ mới để luyện tập.
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Làm sao để phát âm tiếng Pháp chuẩn?',
          summary: 'Phát âm đúng là bước quan trọng khi học tiếng Pháp.',
          slug: 'lam-sao-de-phat-am-tieng-phap-chuan',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Nghe người bản ngữ càng nhiều càng tốt.
      2. Luyện với IPA (ký hiệu phiên âm quốc tế).
      3. Ghi âm giọng nói của bạn để so sánh.
      4. Tập trung vào âm mũi và âm cuối từ.
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Tiếng Pháp trong du lịch: Từ vựng cần nhớ',
          summary: 'Dù đi Paris hay Lyon, bạn nên biết những câu này.',
          slug: 'tieng-phap-trong-du-lich',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Bonjour – Xin chào.
      2. Où est…? – … ở đâu?
      3. Combien ça coûte? – Giá bao nhiêu?
      4. Je voudrais… – Tôi muốn…
      5. Merci – Cảm ơn.
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Cách ghi nhớ giống đực và giống cái trong tiếng Pháp',
          summary: 'Giống danh từ luôn gây khó chịu. Có mẹo đấy!',
          slug: 'cach-ghi-nho-giong-danh-tu',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Ghi chú giống khi học từ mới.
      2. Học theo nhóm từ cùng giới tính.
      3. Dùng hình ảnh để hỗ trợ ghi nhớ.
      4. Làm bài tập phân loại giới tính thường xuyên.
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Tự học tiếng Pháp tại nhà cho người mới bắt đầu',
          summary: 'Bạn hoàn toàn có thể bắt đầu học tiếng Pháp tại nhà!',
          slug: 'tu-hoc-tieng-phap-tai-nha',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Lên thời gian biểu hợp lý.
      2. Kết hợp nhiều tài nguyên khác nhau.
      3. Theo dõi tiến trình hàng tuần.
      4. Luyện nói mỗi ngày, kể cả khi không có ai nghe.
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Top 10 website học tiếng Pháp uy tín',
          summary: 'Internet là kho tài nguyên học tiếng Pháp khổng lồ!',
          slug: 'top-10-website-hoc-tieng-phap',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. TV5MONDE
      2. Français Facile
      3. Duolingo
      4. LingQ
      5. Memrise
      6. Lawless French
      7. FrenchPod101
      8. RFI Savoirs
      9. BBC Languages French
      10. Learn French With Alexa
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Lịch trình học tiếng Pháp trong 30 ngày',
          summary: 'Bắt đầu hành trình học tiếng Pháp hiệu quả trong 1 tháng!',
          slug: 'lich-trinh-hoc-tieng-phap-30-ngay',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      Tuần 1: Học bảng chữ cái, số, chào hỏi.
      Tuần 2: Học danh từ, giới từ, động từ cơ bản.
      Tuần 3: Học phát âm và mẫu câu đơn giản.
      Tuần 4: Luyện nghe – nói – viết mỗi ngày.
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Phương pháp shadowing trong tiếng Pháp',
          summary: 'Shadowing là kỹ thuật học nói cực kỳ hiệu quả.',
          slug: 'phuong-phap-shadowing-tieng-phap',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Chọn video/audio người bản xứ.
      2. Nghe và nhại lại đồng thời.
      3. Ghi âm để so sánh với bản gốc.
      4. Lặp lại nhiều lần cho đến khi nói trôi chảy.
      `,
          thumbnailKey: random(images),
        },
        {
          title: '5 kênh YouTube giúp bạn giỏi tiếng Pháp',
          summary: 'Vừa học vừa giải trí với các YouTuber dạy tiếng Pháp.',
          slug: 'kenh-youtube-hoc-tieng-phap',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Learn French with Alexa
      2. Français Authentique
      3. Comme Une Française
      4. FrenchPod101
      5. StreetFrench.org
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Cách học tiếng Pháp khi không có người luyện cùng',
          summary: 'Học một mình vẫn hiệu quả nếu có chiến lược đúng.',
          slug: 'hoc-tieng-phap-mot-minh',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Ghi âm giọng nói để luyện nói.
      2. Tự đặt câu và viết nhật ký bằng tiếng Pháp.
      3. Tham gia nhóm học online.
      4. Xem video hội thoại và lặp lại.
      `,
          thumbnailKey: random(images),
        },
        {
          title: 'Mẹo ghi nhớ động từ bất quy tắc',
          summary: 'Động từ bất quy tắc không cần học vẹt nếu làm đúng cách.',
          slug: 'meo-ghi-nho-dong-tu-bat-quy-tac',
          categoryId: random(categories).id,
          authorId: admin.id,
          content: `
      1. Tạo nhóm theo đuôi chia giống nhau.
      2. Dùng flashcard hoặc app quiz.
      3. Gắn động từ vào ngữ cảnh cụ thể.
      4. Luyện viết nhật ký với các động từ đó.
      `,
          thumbnailKey: random(images),
        },
      ],
      skipDuplicates: true,
    });

    // Applications
    const jobs = await tx.job.findMany();
    await tx.application.createMany({
      data: range(100).map(() => ({
        personId: random(users).id,
        jobId: random(jobs).id,
        appliedAt: new Date(),
      })),
      skipDuplicates: true,
    });

    // Tags
    const tagsData = [
      'Du học',
      'Luyện thi',
      'Chia sẻ kinh nghiệm',
      'Tư vấn',
      'Học tập',
      'Sinh hoạt',
      'Cộng đồng',
      'Kinh doanh',
      'Tài chính',
      'Chia sẻ',
      'Tâm sự',
      'Học tập',
      'Sinh hoạt',
      'Cộng đồng',
      'Kinh doanh',
      'Tài chính',
      'Chia sẻ',
    ];

    await tx.tag.createMany({
      data: tagsData.flatMap((name) => [
        { name, type: 'JOB' },
        { name, type: 'QUIZ' },
        { name, type: 'ARTICLE' },
      ]),
      skipDuplicates: true,
    });

    // Tags add
    const tags = await tx.tag.findMany();
    const quizzes = await tx.quiz.findMany();
    const articles = await tx.article.findMany();

    for (const job of jobs) {
      await tx.job.update({
        where: { id: job.id },
        data: {
          tags: { connect: pickRandom(tags, 3).map((tag) => ({ id: tag.id })) },
        },
      });
    }

    for (const quiz of quizzes) {
      await tx.quiz.update({
        where: { id: quiz.id },
        data: { tags: { connect: pickRandom(tags, 3).map((tag) => ({ id: tag.id })) } },
      });
    }

    for (const article of articles) {
      await tx.article.update({
        where: { id: article.id },
        data: { tags: { connect: pickRandom(tags, 3).map((tag) => ({ id: tag.id })) } },
      });
    }
  });

  await prisma.$disconnect();
  console.log('Done!');
}

main();
