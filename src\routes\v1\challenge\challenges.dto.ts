import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, SearchSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const CreateChallengeSchema = Type.Object({
  title: Type.String({ minLength: 1 }),
  description: Type.String({ minLength: 1 }),
  thumbnail: Type.Any({ isFile: true }),
  articleUrl: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
});

const UpdateChallengeParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateChallengeBodySchema = Type.Object({
  title: Type.Optional(Type.String({ minLength: 1 })),
  description: Type.Optional(Type.String({ minLength: 1 })),
  thumbnail: Type.Optional(Type.Any({ isFile: true })),
  articleUrl: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
});

const GetChallengeSchema = Type.Object({
  id: Type.Number(),
});

const GetChallengesSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
  tag: Type.Optional(Type.String({ minLength: 1 })),
});

const DeleteChallengeSchema = Type.Object({
  id: Type.Number(),
});

export const ChallengesDTO = {
  CreateChallengeSchema,
  UpdateChallengeParamsSchema,
  UpdateChallengeBodySchema,
  GetChallengeSchema,
  GetChallengesSchema,
  DeleteChallengeSchema,
};

export type ChallengeDTOTypes = StaticFromSchema<typeof ChallengesDTO>;
