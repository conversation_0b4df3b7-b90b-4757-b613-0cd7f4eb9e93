import fp from 'fastify-plugin';
import { IMAGE_MIME_TYPES } from '@/common/constants/mime';
import { MinioBucket, publicPolicy } from '@/common/constants/minio';
import { fileTypeFromBuffer } from 'file-type';
import { Client } from 'minio';
import { v4 as uuidv4 } from 'uuid';

const minioPlugin = fp(async (app) => {
  const minioClient = new Client({
    endPoint: app.config.MINIO_ENDPOINT,
    port: app.config.MINIO_PORT,
    useSSL: false,
    accessKey: app.config.MINIO_ACCESS_KEY,
    secretKey: app.config.MINIO_SECRET_KEY,
  });

  for (const bucket of Object.values(MinioBucket)) {
    const exists = await minioClient.bucketExists(bucket);
    if (!exists) await minioClient.makeBucket(bucket);
  }

  await minioClient.setBucketPolicy(
    MinioBucket.IMAGES,
    JSON.stringify(publicPolicy(MinioBucket.IMAGES)),
  );

  app.decorate('minio', minioClient);
  app.decorate('uploadToMinio', async (file) => {
    const { buffer, fileType, bucket } = file;

    const objectName = uuidv4() + '.' + fileType.ext;
    await minioClient.putObject(bucket, objectName, buffer, buffer.length, {
      'Content-Type': fileType.mime,
    });

    return `${bucket}/${objectName}`;
  });

  app.decorate('uploadImage', async (image: Buffer) => {
    const fileType = await fileTypeFromBuffer(image);
    if (!fileType || !IMAGE_MIME_TYPES.includes(fileType.mime)) throw app.errors.FILE_TYPE_ERROR;

    const key = await app.uploadToMinio({
      buffer: image,
      fileType,
      bucket: MinioBucket.IMAGES,
    });

    return key;
  });

  app.get('/files/:key', async (req, reply) => {
    const { key } = req.params as { key: string };

    try {
      const stat = await minioClient.statObject(MinioBucket.RESUMES, key);
      const stream = await minioClient.getObject(MinioBucket.RESUMES, key);

      if (stat.metaData && stat.metaData['content-type']) {
        reply.header('Content-Type', stat.metaData['content-type']);
      }

      return reply.send(stream);
    } catch {
      throw app.errors.NOT_FOUND;
    }
  });
});

export default minioPlugin;
