import { FastifyRequest } from 'fastify';
import { Role } from '@prisma/client';

import fp from 'fastify-plugin';
import fastifyJwt from '@fastify/jwt';
import usersService from '@/routes/v1/users/users.service';

const jwtPlugin = fp(async (app) => {
  app.register(fastifyJwt, {
    secret: app.config.JWT_ACCESS_SECRET,
  });

  app.decorate('authenticate', async function (request: FastifyRequest): Promise<void> {
    try {
      await request.jwtVerify();
      const { id } = request.user;

      const person = await usersService.getById(id, { omit: {} });
      if (!person) throw app.errors.UNAUTHORIZED;
      request.person = person;
    } catch {
      throw app.errors.UNAUTHORIZED;
    }
  });

  app.decorate('roleAuthenticate', function (roles: Role[]) {
    return async function (request: FastifyRequest): Promise<void> {
      if (!roles.includes(request.person.role)) {
        throw app.errors.FORBIDDEN;
      }
    };
  });
});

export default jwtPlugin;
